// Modal styles
.batch-edit-modal {
  color: white !important;

  // Modal header
  .modal-header {
    background-color: #080808 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    padding: 16px 20px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;

    .modal-title {
      font-size: 20px;
      font-weight: 500;
      margin: 0;
    }

    .close-btn {
      background: none;
      border: none;
      color: rgba(255, 255, 255, 0.7);
      font-size: 18px;
      cursor: pointer;
      transition: color 0.2s ease;

      &:hover {
        color: white;
      }
    }
  }

  // Modal body
  .modal-body {
    background-color: #080808 !important;
    padding: 20px !important;
    max-height: 70vh !important;
    overflow-y: auto !important;

    // Form styles
    form {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .form-row {
        display: flex;
        gap: 20px;

        &.full-width {
          flex-direction: column;
        }

        @media (max-width: 768px) {
          flex-direction: column;
        }
      }

      .form-group {
        flex: 1;
        display: flex;
        flex-direction: column;

        label {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 8px;
          color: #7C7C7C !important;
          transition: color 0.3s ease;
        }

        // Styling for labels of disabled fields
        &.disabled-field-group {
          label {
            color: #555555 !important;
            opacity: 0.7;
          }
        }

        // Styling for enabled fields (highlighted when others are disabled)
        &.enabled-field-group {
          label {
            color: #3CAA56 !important;
            font-weight: 600;
          }
        }

        input,
        select,
        textarea {
          background-color: #080808 !important;
          border: 1px solid #303030 !important;
          border-radius: 6px !important;
          padding: 12px 16px !important;
          color: white !important;
          font-size: 14px !important;
          transition: all 0.3s ease !important;

          &:focus {
            outline: none !important;
            border-color: #3CAA56 !important;
            box-shadow: 0 0 0 2px rgba(60, 170, 86, 0.2) !important;
          }

          &.is-invalid {
            border-color: #e53e3e !important;
            box-shadow: 0 0 0 2px rgba(229, 62, 62, 0.1) !important;
          }

          &::placeholder {
            color: rgba(255, 255, 255, 0.3);
          }

          // Disabled field styling
          &:disabled {
            background-color: #1a1a1a !important;
            border-color: #404040 !important;
            color: #666666 !important;
            cursor: not-allowed !important;
            opacity: 0.6 !important;

            &::placeholder {
              color: rgba(255, 255, 255, 0.15) !important;
            }

            &:focus {
              border-color: #404040 !important;
              box-shadow: none !important;
            }
          }
        }

        // Dropdown styling
        select {
          option {
            padding: 8px;
            margin: 2px 0;

            &:checked {
              background-color: rgba(60, 170, 86, 0.2);
              color: #3CAA56;
            }

            &:hover {
              background-color: rgba(255, 255, 255, 0.1);
            }
          }
        }

        select {
          appearance: none;
          background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6"><path fill="rgba(255, 255, 255, 0.5)" d="M0 0l6 6 6-6z"/></svg>');
          background-repeat: no-repeat;
          background-position: right 16px center;
          padding-right: 40px;
        }

        textarea {
          resize: vertical;
          min-height: 100px;
        }

        .error-message {
          font-size: 12px;
          color: #e53e3e;
          margin-top: 6px;
        }

        .info-message {
          font-size: 12px;
          color: #7A86F2;
          margin-top: 6px;
          display: flex;
          align-items: center;
          gap: 5px;

          i {
            font-size: 14px;
          }
        }

        .disabled-field {
          opacity: 0.7;
          background-color: rgba(0, 0, 0, 0.2) !important;
          border-color: rgba(48, 48, 48, 0.5) !important;
          cursor: not-allowed;
        }

        .hint-text {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.6);
          margin-top: 6px;
          font-style: italic;
          padding-left: 4px;
          border-left: 2px solid rgba(60, 170, 86, 0.4);
        }

        // Multi-select dropdown styles
        .multi-select-dropdown {
          position: relative;

          &.is-invalid {
            .dropdown-toggle {
              border-color: #e53e3e !important;
              box-shadow: 0 0 0 2px rgba(229, 62, 62, 0.1) !important;
            }
          }

          &.disabled {
            .dropdown-toggle {
              background-color: #1a1a1a !important;
              border-color: #404040 !important;
              color: #666666 !important;
              cursor: not-allowed !important;
              opacity: 0.6 !important;
            }
          }

          .dropdown-toggle {
            background-color: #080808 !important;
            border: 1px solid #303030 !important;
            border-radius: 6px !important;
            padding: 12px 16px !important;
            color: white !important;
            font-size: 14px !important;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease !important;

            &:hover {
              border-color: rgba(255, 255, 255, 0.2) !important;
            }

            .placeholder {
              color: rgba(255, 255, 255, 0.3);
            }

            .selected-text {
              color: white;
            }

            i {
              color: rgba(255, 255, 255, 0.5);
              transition: transform 0.3s ease;

              &.rotated {
                transform: rotate(180deg);
              }
            }
          }

          .dropdown-menu {
            position: absolute !important;
            top: 100% !important;
            left: 0 !important;
            right: 0 !important;
            background-color: #080808 !important;
            border: 1px solid #303030 !important;
            border-radius: 6px !important;
            margin-top: 4px !important;
            max-height: 200px !important;
            overflow-y: auto !important;
            z-index: 1000 !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
            display: block !important;

            .loading-item,
            .no-items {
              padding: 12px 16px;
              color: rgba(255, 255, 255, 0.5);
              font-size: 14px;
              text-align: center;
            }

            .dropdown-item {
              padding: 0;

              .checkbox-label {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                cursor: pointer;
                transition: background-color 0.2s ease;
                margin: 0;

                &:hover {
                  background-color: rgba(255, 255, 255, 0.05);
                }

                input[type="checkbox"] {
                  display: none;
                }

                .checkmark {
                  width: 16px;
                  height: 16px;
                  border: 1px solid rgba(255, 255, 255, 0.3);
                  border-radius: 3px;
                  margin-right: 12px;
                  position: relative;
                  transition: all 0.2s ease;

                  &::after {
                    content: '';
                    position: absolute;
                    left: 4px;
                    top: 1px;
                    width: 6px;
                    height: 10px;
                    border: solid white;
                    border-width: 0 2px 2px 0;
                    transform: rotate(45deg);
                    opacity: 0;
                    transition: opacity 0.2s ease;
                  }
                }

                input[type="checkbox"]:checked + .checkmark {
                  background-color: #3CAA56;
                  border-color: #3CAA56;

                  &::after {
                    opacity: 1;
                  }
                }

                .course-title {
                  color: white;
                  font-size: 14px;
                  flex: 1;
                }
              }
            }
          }
        }
      }

      // Form actions
      .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 16px;
        margin-top: 10px;

        .cancel-btn {
          background-color: transparent;
          color: rgba(255, 255, 255, 0.7);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 6px;
          padding: 12px 24px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: white;
          }
        }

        .submit-btn {
          background-color: #3CAA56;
          color: white;
          border: none;
          border-radius: 6px;
          padding: 12px 24px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background-color: lighten(#3CAA56, 5%);
            transform: translateY(-2px);
          }

          &:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
          }
        }

        @media (max-width: 576px) {
          flex-direction: column-reverse;

          .cancel-btn,
          .submit-btn {
            width: 100%;
          }
        }
      }
    }
  }
}
