<div class="batch-edit-modal">
  <div class="modal-header">
    <h2 class="modal-title">Edit Batch</h2>
    <button type="button" class="close-btn" (click)="close()">
      <i class="fa-solid fa-times"></i>
    </button>
  </div>

  <div class="modal-body">
    <form [formGroup]="batchForm" (ngSubmit)="onSubmit()">
      <div class="form-row">
        <div class="form-group" [ngClass]="{'disabled-field-group': isBatchCompleted}">
          <label for="batchName">Batch Name</label>
          <input type="text" id="batchName" formControlName="batchName" placeholder="Enter batch name"
            [ngClass]="{'is-invalid': isInvalid('batchName')}">
          <div class="error-message" *ngIf="isInvalid('batchName')">
            <span *ngIf="batchForm.get('batchName')?.errors?.['required']">Batch name is required</span>
            <span *ngIf="batchForm.get('batchName')?.errors?.['minlength']">Batch name must be at least 3
              characters</span>
            <span *ngIf="batchForm.get('batchName')?.errors?.['maxlength']">Batch name cannot exceed 50
              characters</span>
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group" [ngClass]="{'disabled-field-group': isBatchCompleted}">
          <label for="courseId">Courses (Optional)</label>
          <div class="multi-select-dropdown" [ngClass]="{'is-invalid': isInvalid('courseId'), 'disabled': isBatchCompleted}">
            <div class="dropdown-toggle" (click)="!isBatchCompleted && toggleCourseDropdown()">
              <span *ngIf="selectedCourses.length === 0" class="placeholder">-- Select courses --</span>
              <span *ngIf="selectedCourses.length > 0" class="selected-text">
                {{selectedCourses.length}} course(s) selected
              </span>
            </div>
            <div class="dropdown-menu" *ngIf="isCourseDropdownOpen && !isBatchCompleted">
              <div *ngIf="isLoadingCourses" class="loading-item">Loading courses...</div>
              <div *ngIf="!isLoadingCourses && availableCourses.length === 0" class="no-items">No courses available</div>
              <div *ngFor="let course of availableCourses" class="dropdown-item">
                <label class="checkbox-label">
                  <input
                    type="checkbox"
                    [checked]="isCourseSelected(course.id)"
                    (change)="toggleCourseSelection(course.id, $event)">
                  <span class="checkmark"></span>
                  <span class="course-title">{{course.title}}</span>
                </label>
              </div>
            </div>
          </div>
          <div class="hint-text">Assign courses to this batch (optional)</div>
        </div>

        <div class="form-group" [ngClass]="{'disabled-field-group': isBatchCompleted}">
          <label for="instructorName">Instructor Name</label>
          <input type="text" id="instructorName" formControlName="instructorName" placeholder="Enter instructor name"
            [ngClass]="{'is-invalid': isInvalid('instructorName')}">
          <div class="error-message" *ngIf="isInvalid('instructorName')">
            <span *ngIf="batchForm.get('instructorName')?.errors?.['required']">Instructor name is required</span>
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group" [ngClass]="{'disabled-field-group': isBatchCompleted}">
          <label for="startDate">Start Date</label>
          <input type="date" id="startDate" formControlName="startDate"
            [ngClass]="{'is-invalid': isInvalid('startDate')}">
          <div class="error-message" *ngIf="isInvalid('startDate')">
            <span *ngIf="batchForm.get('startDate')?.errors?.['required']">Start date is required</span>
          </div>
        </div>

        <div class="form-group" [ngClass]="{'enabled-field-group': isBatchCompleted}">
          <label for="endDate">End Date</label>
          <input type="date" id="endDate" formControlName="endDate" [ngClass]="{'is-invalid': isInvalid('endDate')}">
          <div class="error-message" *ngIf="isInvalid('endDate')">
            <span *ngIf="batchForm.get('endDate')?.errors?.['required']">End date is required</span>
          </div>

        </div>
      </div>

      <div class="form-row">
        <div class="form-group" [ngClass]="{'disabled-field-group': isBatchCompleted}">
          <label for="maxStudents">Maximum Students (Optional)</label>
          <input type="number" id="maxStudents" formControlName="maxStudents" placeholder="Enter maximum students"
            [ngClass]="{'is-invalid': isInvalid('maxStudents')}">
          <div class="error-message" *ngIf="isInvalid('maxStudents')">
            <span *ngIf="batchForm.get('maxStudents')?.errors?.['min']">Minimum value is 1</span>
            <span *ngIf="batchForm.get('maxStudents')?.errors?.['max']">Maximum value is 100</span>
          </div>
          <div class="hint-text">Leave empty for unlimited students</div>
        </div>

        <div class="form-group" [ngClass]="{'disabled-field-group': isBatchCompleted}">
          <label for="batchMode">Batch Mode</label>
          <select id="batchMode" formControlName="batchMode" [ngClass]="{'is-invalid': isInvalid('batchMode')}">
            <option value="online">Online</option>
            <option value="offline">Offline</option>
            <option value="hybrid">Hybrid</option>
          </select>
          <div class="error-message" *ngIf="isInvalid('batchMode')">
            <span *ngIf="batchForm.get('batchMode')?.errors?.['required']">Batch mode is required</span>
          </div>
        </div>

        <div class="form-group" [ngClass]="{'disabled-field-group': isBatchActive || isBatchCompleted}">
          <label for="plan">Pricing Plan</label>
          <select id="plan" formControlName="plan" [ngClass]="{'is-invalid': isInvalid('plan'), 'disabled-field': isBatchActive}">
            <option value="starter">Starter Plan</option>
            <option value="pro">Pro Plan</option>
            <option value="elite">Elite Plan</option>
          </select>
          <div class="error-message" *ngIf="isInvalid('plan')">
            <span *ngIf="batchForm.get('plan')?.errors?.['required']">Plan selection is required</span>
          </div>
          <div class="info-message" *ngIf="isBatchActive || isBatchCompleted">
            <i class="fa-solid fa-info-circle"></i> Pricing plan cannot be changed for active or completed batches
          </div>
        </div>
      </div>

      <div class="form-row full-width">
        <div class="form-group" [ngClass]="{'disabled-field-group': isBatchCompleted}">
          <label for="description">Description</label>
          <textarea id="description" formControlName="description" rows="4" placeholder="Enter batch description"
            [ngClass]="{'is-invalid': isInvalid('description')}"></textarea>
          <div class="error-message" *ngIf="isInvalid('description')">
            <span *ngIf="batchForm.get('description')?.errors?.['maxlength']">Description cannot exceed 500
              characters</span>
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="cancel-btn" (click)="close()">Cancel</button>
        <button type="submit" class="submit-btn" [disabled]="isSubmitting">
          <span *ngIf="!isSubmitting">Update Batch</span>
          <span *ngIf="isSubmitting">Updating...</span>
        </button>
      </div>
    </form>
  </div>
</div>
