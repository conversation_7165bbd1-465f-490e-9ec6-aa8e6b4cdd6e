import { Component, Input, OnInit, OnDestroy, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Batch } from '../../models/batch.model';
import { BatchService } from '../../services/batch.service';
import { ToastService } from '../../../../core/services/toast.service';
import {
  CourseService,
  Course,
} from '../../../courses/services/course.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-batch-edit-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './batch-edit-modal.component.html',
  styleUrls: ['./batch-edit-modal.component.less'],
})
export class BatchEditModalComponent implements OnInit, OnDestroy {
  @Input() batch!: Batch;

  batchForm!: FormGroup;
  isSubmitting = false;
  availableCourses: Course[] = [];
  isLoadingCourses = false;
  isBatchActive = false;
  isBatchCompleted = false;
  selectedCourses: string[] = [];
  isCourseDropdownOpen = false;

  private subscriptions: Subscription[] = [];

  constructor(
    public activeModal: NgbActiveModal,
    private fb: FormBuilder,
    private batchService: BatchService,
    private toastService: ToastService,
    private courseService: CourseService
  ) {}

  ngOnInit(): void {
    this.checkBatchStatus();
    this.initForm();
    this.loadAvailableCourses();
  }

  /**
   * Check if the batch is active based on start and end dates
   */
  checkBatchStatus(): void {
    try {
      if (!this.batch.startDate || !this.batch.endDate) {
        this.isBatchActive = false;
        return;
      }

      // Convert dates to Date objects
      const startDate = this.getDateObject(this.batch.startDate);
      const endDate = this.getDateObject(this.batch.endDate);
      const currentDate = new Date();

      // If dates are invalid, default to inactive
      if (
        !startDate ||
        !endDate ||
        isNaN(startDate.getTime()) ||
        isNaN(endDate.getTime())
      ) {
        this.isBatchActive = false;
        return;
      }

      // Normalize dates to compare only the date part (ignore time)
      const currentDateOnly = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());
      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());

      // Batch is active if current date is between start and end dates (inclusive)
      this.isBatchActive = currentDateOnly >= startDateOnly && currentDateOnly <= endDateOnly;
      // Batch is completed if current date is after end date
      this.isBatchCompleted = currentDateOnly > endDateOnly;
      console.log('Batch active status:', this.isBatchActive);
      console.log('Batch completed status:', this.isBatchCompleted);
    } catch (error) {
      console.error('Error checking batch status:', error);
      this.isBatchActive = false;
    }
  }

  /**
   * Convert any date format to a Date object
   */
  getDateObject(date: any): Date | null {
    if (!date) return null;

    try {
      // Handle different date formats
      if (date instanceof Date) {
        return date;
      } else if (typeof date === 'object' && date.seconds) {
        // Firestore Timestamp
        return new Date(
          date.seconds * 1000 + (date.nanoseconds || 0) / 1000000
        );
      } else if (typeof date === 'object' && date._seconds) {
        // Firestore Timestamp (underscore format)
        return new Date(
          date._seconds * 1000 + (date._nanoseconds || 0) / 1000000
        );
      } else if (typeof date === 'number') {
        // Timestamp in milliseconds
        return new Date(date);
      } else if (typeof date === 'string') {
        // ISO string or other string format
        return new Date(date);
      }

      return null;
    } catch (error) {
      console.error('Error converting date:', error);
      return null;
    }
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  /**
   * Initialize the form with batch data
   */
  initForm(): void {
    // Initialize selected courses from existing courseId
    if (this.batch.courseId) {
      this.selectedCourses = this.batch.courseId.split(',').filter(id => id.trim());
    }
    this.batchForm = this.fb.group({
      batchName: [
        {
          value: this.batch.batchName || this.batch.name || '',
          disabled: this.isBatchCompleted
        },
        [
          Validators.required,
          Validators.minLength(3),
          Validators.maxLength(50),
        ],
      ],
      courseId: [
        {
          value: this.batch.courseId || '',
          disabled: this.isBatchCompleted
        }
      ], // Optional course ID
      startDate: [
        {
          value: this.formatDateForInput(this.batch.startDate),
          disabled: this.isBatchCompleted
        },
        [Validators.required],
      ],
      endDate: [
        this.formatDateForInput(this.batch.endDate),
        [Validators.required],
      ],
      maxStudents: [
        {
          value: this.batch.maxStudents || null,
          disabled: this.isBatchCompleted
        },
        [Validators.min(1), Validators.max(100)],
      ],
      description: [
        {
          value: this.batch.description || '',
          disabled: this.isBatchCompleted
        },
        [Validators.maxLength(500)]
      ],
      instructorName: [
        {
          value: this.batch.trainer || '',
          disabled: this.isBatchCompleted
        },
        [Validators.required]
      ],
      batchMode: [
        {
          value: this.batch.batchMode || 'online',
          disabled: this.isBatchCompleted
        },
        [Validators.required]
      ],
      plan: [
        {
          value: this.batch.plan || 'starter',
          disabled: this.isBatchActive || this.isBatchCompleted
        },
        [Validators.required]
      ],
    });
  }

  /**
   * Load available courses for the dropdown
   */
  loadAvailableCourses(): void {
    this.isLoadingCourses = true;

    const coursesSub = this.courseService.getAllCourses().subscribe({
      next: (courses) => {
        this.availableCourses = courses;
        this.isLoadingCourses = false;
      },
      error: (error) => {
        console.error('Error loading courses:', error);
        this.toastService.error('Failed to load available courses');
        this.isLoadingCourses = false;
      },
    });

    this.subscriptions.push(coursesSub);
  }

  /**
   * Format a date for the input field
   */
  formatDateForInput(date: any): string {
    if (!date) return '';

    try {
      let dateObj: Date;

      // Handle different date formats
      if (date instanceof Date) {
        dateObj = date;
      } else if (typeof date === 'object' && date.seconds) {
        // Firestore Timestamp
        dateObj = new Date(
          date.seconds * 1000 + (date.nanoseconds || 0) / 1000000
        );
      } else if (typeof date === 'object' && date._seconds) {
        // Firestore Timestamp (underscore format)
        dateObj = new Date(
          date._seconds * 1000 + (date._nanoseconds || 0) / 1000000
        );
      } else if (typeof date === 'number') {
        // Timestamp in milliseconds
        dateObj = new Date(date);
      } else if (typeof date === 'string') {
        // ISO string or other string format
        dateObj = new Date(date);
      } else {
        console.warn('Unknown date format:', date);
        return '';
      }

      // Check if the date is valid
      if (isNaN(dateObj.getTime())) {
        console.warn('Invalid date:', date);
        return '';
      }

      // Format as YYYY-MM-DD for input[type="date"]
      const year = dateObj.getFullYear();
      const month = String(dateObj.getMonth() + 1).padStart(2, '0');
      const day = String(dateObj.getDate()).padStart(2, '0');

      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error('Error formatting date for input:', error, date);
      return '';
    }
  }

  /**
   * Close dropdown when clicking outside
   */
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const dropdown = target.closest('.multi-select-dropdown');
    if (!dropdown) {
      this.isCourseDropdownOpen = false;
    }
  }

  /**
   * Toggle course dropdown visibility
   */
  toggleCourseDropdown(): void {
    this.isCourseDropdownOpen = !this.isCourseDropdownOpen;
  }

  /**
   * Check if a course is selected
   */
  isCourseSelected(courseId: string): boolean {
    return this.selectedCourses.includes(courseId);
  }

  /**
   * Toggle course selection
   */
  toggleCourseSelection(courseId: string, event: any): void {
    if (event.target.checked) {
      if (!this.selectedCourses.includes(courseId)) {
        this.selectedCourses.push(courseId);
      }
    } else {
      this.selectedCourses = this.selectedCourses.filter(id => id !== courseId);
    }

    // Update the form control with comma-separated string
    const courseIdString = this.selectedCourses.join(',');
    this.batchForm.get('courseId')?.setValue(courseIdString);
  }

  /**
   * Check if a form field is invalid
   */
  isInvalid(fieldName: string): boolean {
    const field = this.batchForm.get(fieldName);
    return field ? field.invalid && (field.dirty || field.touched) : false;
  }

  /**
   * Submit the form to update the batch
   */
  onSubmit(): void {
    if (this.batchForm.invalid) {
      // Mark all fields as touched to show validation errors
      Object.keys(this.batchForm.controls).forEach((key) => {
        const control = this.batchForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;

    // Get the form values (including disabled controls)
    const formValues = this.batchForm.getRawValue();

    // Create the update data
    const updateData = {
      ...formValues,
      batchId: this.batch.batchId, // Keep the original batch ID
      batchNumber: this.batch.batchNumber, // Keep the original batch number
    };

    // Log the update data
    console.log('Updating batch with data:', updateData);

    // Call the batch service to update the batch
    this.batchService.updateBatch(updateData).subscribe({
      next: (success) => {
        this.isSubmitting = false;

        if (success) {
          this.toastService.success('Batch updated successfully');
          this.activeModal.close(true); // Close with success result
        } else {
          this.toastService.error('Failed to update batch');
        }
      },
      error: (error) => {
        this.isSubmitting = false;
        console.error('Error updating batch:', error);
        this.toastService.error('Failed to update batch. Please try again.');
      },
    });
  }

  /**
   * Close the modal
   */
  close(): void {
    this.activeModal.dismiss('cancel');
  }
}
