import { Component, OnInit, On<PERSON><PERSON>roy, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { HeaderService } from '../../../core/services/header.service';
import { ToastService } from '../../../core/services/toast.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { InviteLinkModalComponent } from '../../../shared/components/invite-link-modal/invite-link-modal.component';
import { BatchService } from '../services/batch.service';
import { CourseService, Course } from '../../courses/services/course.service';
import { Subscription } from 'rxjs';
import { AdminActivityService } from '../../../core/services/admin-activity.service';

@Component({
  selector: 'app-batch-create',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './batch-create.component.html',
  styleUrls: ['./batch-create.component.less'],
})
export class BatchCreateComponent implements OnInit, OnDestroy {
  batchForm!: FormGroup;
  isSubmitting = false;
  availableCourses: Course[] = [];
  isLoadingCourses = false;
  selectedCourses: string[] = [];
  isCourseDropdownOpen = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private headerService: HeaderService,
    private toastService: ToastService,
    private modalService: NgbModal,
    private batchService: BatchService,
    private courseService: CourseService,
    private adminActivityService: AdminActivityService
  ) {}

  ngOnInit(): void {
    this.headerService.setPageTitle('Create Batch');
    this.initForm();
    this.loadAvailableCourses();
    this.loadNextBatchNumber();

    // We're not logging page access or process starts anymore
    // Only logging completed key actions
  }

  /**
   * Load the next available batch number
   */
  loadNextBatchNumber(): void {
    const nextBatchNumberSub = this.batchService
      .getNextBatchNumber()
      .subscribe({
        next: (nextBatchNumber) => {
          console.log('Next available batch number:', nextBatchNumber);

          // Set the batch number in the form
          this.batchForm.get('batchNumber')?.setValue(nextBatchNumber);

          // Disable the batch number field but keep its value for validation
          this.batchForm
            .get('batchNumber')
            ?.disable({ onlySelf: true, emitEvent: false });
        },
        error: (error) => {
          console.error('Error loading next batch number:', error);
          // Keep the field enabled if there's an error
        },
      });

    this.subscriptions.push(nextBatchNumberSub);
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  /**
   * Load available courses for the dropdown
   */
  loadAvailableCourses(): void {
    this.isLoadingCourses = true;

    const coursesSub = this.courseService.getAllCourses().subscribe({
      next: (courses) => {
        this.availableCourses = courses;
        this.isLoadingCourses = false;
      },
      error: (error) => {
        console.error('Error loading courses:', error);
        this.toastService.error('Failed to load available courses');
        this.isLoadingCourses = false;
      },
    });

    this.subscriptions.push(coursesSub);
  }

  /**
   * Initialize the batch creation form
   */
  initForm(): void {
    this.batchForm = this.fb.group({
      batchName: [
        '',
        [
          Validators.required,
          Validators.minLength(3),
          Validators.maxLength(50),
        ],
      ],
      batchNumber: [
        '',
        [
          Validators.required,
          Validators.pattern('^[0-9]+$'), // Only allow numbers
          Validators.maxLength(10),
        ],
      ],
      courseId: [''], // Optional course ID
      startDate: ['', [Validators.required]],
      endDate: ['', [Validators.required]],
      maxStudents: [null, [Validators.min(1), Validators.max(100)]],
      description: ['', [Validators.maxLength(500)]],
      instructorName: ['', [Validators.required]],
      batchMode: ['online', [Validators.required]],
      plan: ['starter', [Validators.required]], // Default to starter plan
    });
  }

  /**
   * Close dropdown when clicking outside
   */
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const dropdown = target.closest('.multi-select-dropdown');
    if (!dropdown) {
      this.isCourseDropdownOpen = false;
    }
  }

  /**
   * Toggle course dropdown visibility
   */
  toggleCourseDropdown(): void {
    this.isCourseDropdownOpen = !this.isCourseDropdownOpen;
  }

  /**
   * Check if a course is selected
   */
  isCourseSelected(courseId: string): boolean {
    return this.selectedCourses.includes(courseId);
  }

  /**
   * Toggle course selection
   */
  toggleCourseSelection(courseId: string, event: any): void {
    if (event.target.checked) {
      if (!this.selectedCourses.includes(courseId)) {
        this.selectedCourses.push(courseId);
      }
    } else {
      this.selectedCourses = this.selectedCourses.filter(id => id !== courseId);
    }

    // Update the form control with comma-separated string
    const courseIdString = this.selectedCourses.join(',');
    this.batchForm.get('courseId')?.setValue(courseIdString);
  }

  /**
   * Submit the form to create a new batch
   */
  onSubmit(): void {
    // Check if the form is valid (ignoring disabled controls which are already valid)
    if (this.batchForm.invalid) {
      // Mark all enabled fields as touched to trigger validation messages
      Object.keys(this.batchForm.controls).forEach((key) => {
        const control = this.batchForm.get(key);
        if (control && !control.disabled) {
          control.markAsTouched();
        }
      });
      this.toastService.error('Please fill all required fields correctly');
      return;
    }

    this.isSubmitting = true;

    // Get form values and prepare data for submission
    // Include disabled controls in the form value
    const formValue = {
      ...this.batchForm.value,
      batchNumber: this.batchForm.get('batchNumber')?.value || '',
    };

    // Additional validation for batch number
    if (!formValue.batchNumber || !/^\d+$/.test(formValue.batchNumber)) {
      this.toastService.error('Batch number must contain only digits');
      this.isSubmitting = false;
      return;
    }

    // Log the form data for debugging
    console.log('Form data to be submitted:', formValue);

    // Validate dates if provided
    if (formValue.startDate && formValue.endDate) {
      const startDate = new Date(formValue.startDate);
      const endDate = new Date(formValue.endDate);

      // Check if end date is after start date
      if (endDate < startDate) {
        this.toastService.error('End date must be after start date');
        this.isSubmitting = false;
        return;
      }

      // Calculate duration in days first
      const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      // Convert days to weeks with one decimal place precision
      const diffWeeks = parseFloat((diffDays / 7).toFixed(1));
      console.log(`Batch duration: ${diffWeeks} weeks`);
    }

    // Call the batch service to create the batch with the complete form value (including disabled fields)
    this.batchService.createBatch(formValue).subscribe({
      next: (batchId) => {
        this.isSubmitting = false;

        if (batchId) {
          const batchName = this.batchForm.get('batchName')?.value;
          // Get the batch number directly from the disabled control
          const batchNumber = this.batchForm.get('batchNumber')?.value;

          // Log the successful batch creation (key completed action)
          this.adminActivityService
            .logActivity('batch', 'created', {
              batchId: batchId,
              batchName: batchName,
              batchNumber: batchNumber, // Use the value from the disabled control
              courseId: formValue.courseId || null,
              startDate: formValue.startDate,
              endDate: formValue.endDate,
              instructorName: formValue.instructorName,
              batchMode: formValue.batchMode,
              plan: formValue.plan,
            })
            .subscribe();

          // Show success message
          this.toastService.success(
            `Batch "${batchName}" created successfully`
          );

          // Verify the batch exists before showing the invite link modal
          this.verifyBatchAndShowInviteLink(batchId, batchName);

          // Navigate to the newly created batch details page
          // Extract the batch number from the batchId (remove 'batch' prefix)
          const routeBatchNumber = batchId.replace('batch', '');
          this.router.navigate(['/batches', routeBatchNumber]);
        } else {
          // Not logging failures - only completed key actions
          this.toastService.error('Failed to create batch. Please try again.');
        }
      },
      error: (error) => {
        this.isSubmitting = false;
        console.error('Error creating batch:', error);

        // Not logging errors - only completed key actions
        this.toastService.error('Failed to create batch. Please try again.');
      },
    });
  }

  /**
   * Verify that the batch exists before showing the invite link modal
   * @param batchId The ID of the created batch
   * @param batchName The name of the created batch
   */
  private verifyBatchAndShowInviteLink(
    batchId: string,
    batchName: string
  ): void {
    // Add a small delay to ensure Firebase has time to process the write
    setTimeout(() => {
      // Verify the batch exists by trying to get it
      this.batchService.getBatchById(batchId).subscribe({
        next: (batch) => {
          if (batch) {
            // Batch exists, show the invite link modal
            this.showInviteLinkModal(batchId, batchName);
          } else {
            console.warn(`Batch ${batchId} not found, retrying...`);
            // Retry after a short delay
            setTimeout(() => {
              this.batchService.getBatchById(batchId).subscribe({
                next: (retryBatch) => {
                  if (retryBatch) {
                    this.showInviteLinkModal(batchId, batchName);
                  } else {
                    console.error(
                      `Batch ${batchId} still not found after retry`
                    );
                    // Show the invite link modal anyway, as the batch might exist
                    // but there could be permission issues or other problems
                    this.showInviteLinkModal(batchId, batchName);
                  }
                },
                error: (error) => {
                  console.error(`Error verifying batch ${batchId}:`, error);
                  // Show the invite link modal anyway
                  this.showInviteLinkModal(batchId, batchName);
                },
              });
            }, 1000);
          }
        },
        error: (error) => {
          console.error(`Error verifying batch ${batchId}:`, error);
          // Show the invite link modal anyway
          this.showInviteLinkModal(batchId, batchName);
        },
      });
    }, 500);
  }

  /**
   * Show the invite link modal
   * @param batchId The ID of the created batch
   * @param batchName The name of the created batch
   */
  private showInviteLinkModal(batchId: string, batchName: string): void {
    const modalRef = this.modalService.open(InviteLinkModalComponent, {
      centered: true,
      backdrop: 'static',
      keyboard: false,
      windowClass: 'invite-link-modal-window',
    });

    // Pass data to the modal
    modalRef.componentInstance.batchId = batchId;
    modalRef.componentInstance.batchName = batchName;
  }

  /**
   * Check if a form control is invalid and touched
   * @param controlName The name of the form control
   * @returns True if the control is invalid and touched
   */
  isInvalid(controlName: string): boolean {
    const control = this.batchForm.get(controlName);
    // Disabled controls are always considered valid
    return (
      !!control &&
      !control.disabled &&
      control.invalid &&
      (control.dirty || control.touched)
    );
  }

  /**
   * Navigate back to the batches list
   */
  goBack(): void {
    // Not logging cancellations - only completed key actions
    this.router.navigate(['/batches']);
  }
}
