// Main container styles
.batch-create-container {
  padding: 20px;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  max-width: 1000px;
  margin: 0 auto;
  overflow-y: auto;
}

// Back button styles
.back-button-container {
  margin-bottom: 20px;

  .back-btn {
    background-color: transparent;
    color: rgba(255, 255, 255, 0.7);
    border: none;
    padding: 8px 0;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;

    &:hover {
      color: white;
      transform: translateX(-3px);
    }

    i {
      font-size: 12px;
      transition: transform 0.3s ease;
    }

    &:hover i {
      transform: translateX(-3px);
    }
  }
}

// Page header styles
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;

  .page-title {
    font-size: 24px;
    font-weight: 500;
    color: #fff;
    margin: 0;
  }

  .view-all-batches {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 8px 12px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.05);

    &:hover {
      color: white;
      background-color: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
    }

    i {
      font-size: 12px;
    }
  }
}

// Form styles
.batch-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
  overflow-y: auto;

  .form-row {
    display: flex;
    gap: 20px;

    &.full-width {
      flex-direction: column;
    }

    .form-group {
      flex: 1;
      display: flex;
      flex-direction: column;

      label {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 8px;
      }

      input,
      select,
      textarea {
        background-color: #1a1a1a;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 6px;
        padding: 12px 16px;
        color: white;
        font-size: 14px;
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: #3CAA56;
          box-shadow: 0 0 0 2px rgba(60, 170, 86, 0.2);
        }

        &.is-invalid {
          border-color: #e53e3e;
          box-shadow: 0 0 0 2px rgba(229, 62, 62, 0.1);
        }

        &::placeholder {
          color: rgba(255, 255, 255, 0.3);
        }

        &:disabled,
        &[readonly] {
          background-color: #252525;
          border-color: rgba(255, 255, 255, 0.15);
          color: rgba(255, 255, 255, 0.8);
          cursor: not-allowed;
          opacity: 0.9;
          box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
        }
      }

      // Dropdown styling
      select {
        option {
          padding: 8px;
          margin: 2px 0;

          &:checked {
            background-color: rgba(60, 170, 86, 0.2);
            color: #3CAA56;
          }

          &:hover {
            background-color: rgba(255, 255, 255, 0.1);
          }
        }

        &#plan {
          option[value="starter"] {
            background-color: #2A2A2A;
          }

          option[value="pro"] {
            background-color: #2A2A2A;
            font-weight: 500;
          }

          option[value="elite"] {
            background-color: #2A2A2A;
            font-weight: 600;
          }
        }
      }

      select {
        appearance: none;
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6"><path fill="rgba(255, 255, 255, 0.5)" d="M0 0l6 6 6-6z"/></svg>');
        background-repeat: no-repeat;
        background-position: right 16px center;
        padding-right: 40px;
      }

      textarea {
        resize: vertical;
        min-height: 100px;
      }

      .error-message {
        font-size: 12px;
        color: #e53e3e;
        margin-top: 6px;
      }

      .hint-text {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        margin-top: 6px;
        padding: 6px 8px;
        border-radius: 4px;
        background-color: rgba(60, 170, 86, 0.1);
        border-left: 2px solid rgba(60, 170, 86, 0.6);

        i {
          color: #3CAA56;
          margin-right: 4px;
        }
      }

      // Multi-select dropdown styles
      .multi-select-dropdown {
        position: relative;

        &.is-invalid {
          .dropdown-toggle {
            border-color: #e53e3e;
            box-shadow: 0 0 0 2px rgba(229, 62, 62, 0.1);
          }
        }

        .dropdown-toggle {
          background-color: #1a1a1a !important;
          border: 1px solid rgba(255, 255, 255, 0.1) !important;
          border-radius: 6px !important;
          padding: 12px 16px !important;
          color: white !important;
          font-size: 14px !important;
          cursor: pointer !important;
          display: flex !important;
          justify-content: space-between !important;
          align-items: center !important;
          transition: all 0.3s ease !important;
          pointer-events: auto !important;
          user-select: none !important;

          &:hover {
            border-color: rgba(255, 255, 255, 0.2);
          }

          .placeholder {
            color: rgba(255, 255, 255, 0.3);
          }

          .selected-text {
            color: white;
          }

          i {
            color: rgba(255, 255, 255, 0.5);
            transition: transform 0.3s ease;

            &.rotated {
              transform: rotate(180deg);
            }
          }
        }

        .dropdown-menu {
          position: absolute;
          top: 100%;
          left: 0;
          right: 0;
          background-color: #1a1a1a;
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 6px;
          margin-top: 4px;
          max-height: 200px;
          overflow-y: auto;
          z-index: 1000;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

          .loading-item,
          .no-items {
            padding: 12px 16px;
            color: rgba(255, 255, 255, 0.5);
            font-size: 14px;
            text-align: center;
          }

          .dropdown-item {
            padding: 0;

            .checkbox-label {
              display: flex;
              align-items: center;
              padding: 12px 16px;
              cursor: pointer;
              transition: background-color 0.2s ease;
              margin: 0;

              &:hover {
                background-color: rgba(255, 255, 255, 0.05);
              }

              input[type="checkbox"] {
                display: none;
              }

              .checkmark {
                width: 16px;
                height: 16px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 3px;
                margin-right: 12px;
                position: relative;
                transition: all 0.2s ease;

                &::after {
                  content: '';
                  position: absolute;
                  left: 4px;
                  top: 1px;
                  width: 6px;
                  height: 10px;
                  border: solid white;
                  border-width: 0 2px 2px 0;
                  transform: rotate(45deg);
                  opacity: 0;
                  transition: opacity 0.2s ease;
                }
              }

              input[type="checkbox"]:checked + .checkmark {
                background-color: #3CAA56;
                border-color: #3CAA56;

                &::after {
                  opacity: 1;
                }
              }

              .course-title {
                color: white;
                font-size: 14px;
                flex: 1;
              }
            }
          }
        }
      }
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    position: sticky;
    bottom: 0;
    padding: 20px 0;
    backdrop-filter: blur(5px);

    .cancel-btn {
      background-color: transparent;
      color: rgba(255, 255, 255, 0.7);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 6px;
      padding: 12px 24px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(255, 255, 255, 0.05);
        color: white;
      }
    }

    .submit-btn {
      background-color: #F7D44C;
      color: #111;
      border: none;
      border-radius: 6px;
      padding: 12px 24px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: lighten(#F7D44C, 5%);
        transform: translateY(-2px);
      }

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
      }
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .batch-form {
    .form-row {
      flex-direction: column;
      gap: 15px;
    }

    .form-actions {
      flex-direction: column-reverse;

      .cancel-btn,
      .submit-btn {
        width: 100%;
      }
    }
  }
}

@media (max-width: 576px) {
  .batch-create-container {
    padding: 15px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;

    .page-title {
      font-size: 20px;
    }

    .view-all-batches {
      width: 100%;
      justify-content: center;
    }
  }

  .batch-form {
    gap: 15px;

    .form-group {
      label {
        font-size: 13px;
      }

      input,
      select,
      textarea {
        padding: 10px 14px;
        font-size: 13px;
      }
    }

    .form-actions {
      padding: 15px 0;
      margin-top: 20px;

      .cancel-btn,
      .submit-btn {
        padding: 10px 20px;
        font-size: 13px;
      }
    }
  }
}

@media (max-height: 700px) {
  .batch-form {
    gap: 12px;

    .form-row {
      gap: 12px;
    }

    .form-group {
      label {
        margin-bottom: 4px;
      }

      input,
      select {
        padding: 8px 12px;
      }

      textarea {
        min-height: 80px;
      }
    }
  }
}
