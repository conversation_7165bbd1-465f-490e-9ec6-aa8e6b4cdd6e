<div class="batch-create-container">
  <div class="back-button-container">
    <button class="back-btn" (click)="goBack()">
      <i class="fa-solid fa-arrow-left"></i> <span>Back</span>
    </button>
  </div>



  <form [formGroup]="batchForm" (ngSubmit)="onSubmit()" class="batch-form">
    <div class="form-row">
      <div class="form-group">
        <label for="batchName">Batch Name</label>
        <input type="text" id="batchName" formControlName="batchName" placeholder="Ex: Java Coders"
          [ngClass]="{'is-invalid': isInvalid('batchName')}">
        <div class="error-message" *ngIf="isInvalid('batchName')">
          <span *ngIf="batchForm.get('batchName')?.errors?.['required']">Batch name is required</span>
          <span *ngIf="batchForm.get('batchName')?.errors?.['minlength']">Batch name must be at least 3
            characters</span>
        </div>
      </div>

      <div class="form-group">
        <label for="batchNumber">Batch Number</label>
        <input type="text" id="batchNumber" formControlName="batchNumber" placeholder="Auto-generated batch number"
          [ngClass]="{'is-invalid': isInvalid('batchNumber')}" readonly>

        <div class="error-message" *ngIf="isInvalid('batchNumber')">
          <span *ngIf="batchForm.get('batchNumber')?.errors?.['required']">Batch number is required</span>
          <span *ngIf="batchForm.get('batchNumber')?.errors?.['pattern']">Batch number must contain only digits</span>
          <span *ngIf="batchForm.get('batchNumber')?.errors?.['maxlength']">Batch number is too long</span>
        </div>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="startDate">Start Date</label>
        <input type="date" id="startDate" formControlName="startDate"
          [ngClass]="{'is-invalid': isInvalid('startDate')}">
        <div class="error-message" *ngIf="isInvalid('startDate')">
          <span *ngIf="batchForm.get('startDate')?.errors?.['required']">Start date is required</span>
        </div>
      </div>

      <div class="form-group">
        <label for="endDate">End Date</label>
        <input type="date" id="endDate" formControlName="endDate" [ngClass]="{'is-invalid': isInvalid('endDate')}">
        <div class="error-message" *ngIf="isInvalid('endDate')">
          <span *ngIf="batchForm.get('endDate')?.errors?.['required']">End date is required</span>
        </div>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="courseId">Courses (Optional)</label>
        <!-- Debug info -->
        <div style="font-size: 12px; color: #666; margin-bottom: 5px;">
          Debug: Dropdown Open: {{isCourseDropdownOpen}}, Courses: {{availableCourses.length}}, Loading: {{isLoadingCourses}}
        </div>
        <div class="multi-select-dropdown" [ngClass]="{'is-invalid': isInvalid('courseId')}">
          <div class="dropdown-toggle" (click)="toggleCourseDropdown(); $event.stopPropagation()">
            <span *ngIf="selectedCourses.length === 0" class="placeholder">-- Select courses --</span>
            <span *ngIf="selectedCourses.length > 0" class="selected-text">
              {{selectedCourses.length}} course(s) selected
            </span>
            <i class="fa-solid fa-chevron-down" [class.rotated]="isCourseDropdownOpen"></i>
          </div>
          <div class="dropdown-menu" *ngIf="isCourseDropdownOpen">
            <div *ngIf="isLoadingCourses" class="loading-item">Loading courses...</div>
            <div *ngIf="!isLoadingCourses && availableCourses.length === 0" class="no-items">No courses available</div>
            <div *ngFor="let course of availableCourses" class="dropdown-item">
              <label class="checkbox-label">
                <input
                  type="checkbox"
                  [checked]="isCourseSelected(course.id)"
                  (change)="toggleCourseSelection(course.id, $event)">
                <span class="checkmark"></span>
                <span class="course-title">{{course.title}}</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <div class="form-group">
        <label for="plan">Pricing Plan</label>
        <select id="plan" formControlName="plan" [ngClass]="{'is-invalid': isInvalid('plan')}">
          <option value="starter">Starter Plan</option>
          <option value="pro">Pro Plan</option>
          <option value="elite">Elite Plan</option>
        </select>
        <div class="error-message" *ngIf="isInvalid('plan')">
          <span *ngIf="batchForm.get('plan')?.errors?.['required']">Plan selection is required</span>
        </div>
      </div>

      <div class="form-group">
        <label for="instructorName">Instructor Name</label>
        <input type="text" id="instructorName" formControlName="instructorName" placeholder="Enter instructor name"
          [ngClass]="{'is-invalid': isInvalid('instructorName')}">
        <div class="error-message" *ngIf="isInvalid('instructorName')">
          <span *ngIf="batchForm.get('instructorName')?.errors?.['required']">Instructor name is required</span>
        </div>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="maxStudents">Maximum Students (Optional)</label>
        <input type="number" id="maxStudents" formControlName="maxStudents" min="1" max="100"
          [ngClass]="{'is-invalid': isInvalid('maxStudents')}">
        <div class="error-message" *ngIf="isInvalid('maxStudents')">
          <span *ngIf="batchForm.get('maxStudents')?.errors?.['min']">Minimum value is 1</span>
          <span *ngIf="batchForm.get('maxStudents')?.errors?.['max']">Maximum value is 100</span>
        </div>
      </div>

      <div class="form-group">
        <label for="batchMode">Batch Mode</label>
        <select id="batchMode" formControlName="batchMode" [ngClass]="{'is-invalid': isInvalid('batchMode')}">
          <option value="online">Online</option>
          <option value="offline">Offline</option>
          <option value="hybrid">Hybrid</option>
        </select>
        <div class="error-message" *ngIf="isInvalid('batchMode')">
          <span *ngIf="batchForm.get('batchMode')?.errors?.['required']">Batch mode is required</span>
        </div>
      </div>
    </div>

    <div class="form-row full-width">
      <div class="form-group">
        <label for="description">Description</label>
        <textarea id="description" formControlName="description" rows="4" placeholder="Enter batch description"
          [ngClass]="{'is-invalid': isInvalid('description')}"></textarea>
        <div class="error-message" *ngIf="isInvalid('description')">
          <span *ngIf="batchForm.get('description')?.errors?.['maxlength']">Description cannot exceed 500
            characters</span>
        </div>
      </div>
    </div>

    <div class="form-actions">
      <button type="button" class="cancel-btn" (click)="goBack()">Cancel</button>
      <button type="submit" class="submit-btn" [disabled]="isSubmitting">
        <span *ngIf="!isSubmitting">Create Batch</span>
        <span *ngIf="isSubmitting">Creating...</span>
      </button>
    </div>
  </form>
</div>