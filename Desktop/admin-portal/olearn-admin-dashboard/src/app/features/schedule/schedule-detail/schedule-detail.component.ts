import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { HeaderService } from '../../../core/services/header.service';
import { ToastService } from '../../../core/services/toast.service';
import { Schedule } from '../models/schedule.model';
import { ScheduleDataService } from '../services/schedule-data.service';
import { FirestoreService } from '../../../core/services/firestore.service';
import { Subscription } from 'rxjs';
import * as ExcelJS from 'exceljs';
import { Auth } from '@angular/fire/auth';
import { AiService } from '../../../core/services/ai.service';
import { ChallengeGenerationComponent } from '../components/challenge-generation/challenge-generation.component';
import { InterviewGenerationComponent } from '../components/interview-generation/interview-generation.component';

// Using any type for participants to support multiple event types
type Participant = any;

@Component({
  selector: 'app-schedule-detail',
  standalone: true,
  imports: [CommonModule, FormsModule, ChallengeGenerationComponent, InterviewGenerationComponent],
  templateUrl: './schedule-detail.component.html',
  styleUrls: ['./schedule-detail.component.less'],
})
export class ScheduleDetailComponent implements OnInit, OnDestroy {
  // Schedule data
  scheduleId: string = '';
  scheduleTitle: string = '';
  scheduleType: string = '';
  scheduleCategory: string = '';
  scheduleDifficulty: string = '';

  // Full schedule object
  schedule: Schedule | null = null;

  // Additional schedule details
  scheduleCreatedAt: number | null = null;
  scheduleDate: number | null = null;
  scheduleTime: string = '';
  numberOfQuestions: number | null = null;
  testDuration: string = '';
  assignedBatches: string[] = [];

  // Loading state
  isLoading: boolean = true;

  // Subscriptions
  private routeSubscription: Subscription | null = null;

  // Sample participant data
  participants: Participant[] = [];

  // Tab management
  activeTab: 'questions' | 'results' | 'settings' = 'questions';

  // Content management
  generatedQuestions: any[] = [];
  selectedQuestionIndices: number[] = [];
  isGenerating: boolean = false;
  isSaving: boolean = false;
  isPublishing: boolean = false;

  // Loading animation
  currentLoadingText: string = '';
  private loadingMessages: string[] = [
    'Fetching questions...',
    'AI preparing content...',
    'Analyzing requirements...',
    'Crafting perfect questions...',
    'Almost ready...'
  ];
  private typingInterval: any;
  private messageInterval: any;

  // Question expansion
  expandedQuestions: Set<string> = new Set();

  // AI Integration for textarea
  aiPrompt: string = '';
  isGeneratingFromPrompt: boolean = false;
  smartSuggestions: string[] = [];
  recentlyDeletedTopics: string[] = [];

  // PDF Upload functionality
  selectedFile: File | null = null;
  isDragOver: boolean = false;
  isGeneratingFromPDF: boolean = false;

  // Sorting
  sortColumn: string = 'score'; // Default sort column
  sortDirection: 'asc' | 'desc' = 'desc'; // Default sort direction

  // Event type detection
  eventType: 'assessment' | 'interview' | 'challenge' | 'unknown' = 'unknown';

  // ViewChild references to child components
  @ViewChild('interviewComponent') interviewComponent!: InterviewGenerationComponent;
  @ViewChild('challengeComponent') challengeComponent!: ChallengeGenerationComponent;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    private headerService: HeaderService,
    private toastService: ToastService,
    private scheduleDataService: ScheduleDataService,
    private firestoreService: FirestoreService,
    private auth: Auth,
    private aiService: AiService
  ) {}

  // Store Firebase subscriptions
  private scheduleSubscription: Subscription | null = null;
  private resultsSubscription: Subscription | null = null;

  // Organization ID for Firebase paths
  private orgId: string | null = null;

  ngOnInit(): void {
    this.isLoading = true;

    // Get organization ID from URL or user context
    this.getOrganizationId();

    // Get the schedule ID from the route parameters
    this.routeSubscription = this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.scheduleId = id;

        // Try to get schedule data from the service
        const scheduleData = this.scheduleDataService.getCurrentSchedule();

        if (scheduleData) {
          console.log('Using schedule data from service:', scheduleData);
          this.setScheduleDetails(scheduleData);

          // Fetch the schedule results even when we have the schedule data
          this.fetchScheduleResults(id);
        } else {
          this.fetchScheduleFromFirebase(id);
        }
      }
    });
  }

  /**
   * Get the organization ID for Firebase paths
   */
  private getOrganizationId(): void {
    // Get the current Firebase user

    const firebaseUser = this.auth.currentUser;

    if (firebaseUser && firebaseUser.displayName) {
      // Extract organization ID from displayName (format: orgId_role)
      const parts = firebaseUser.displayName.split('_');
      if (parts.length > 0) {
        this.orgId = parts[0].trim();
        console.log(`Organization ID from Firebase user: ${this.orgId}`);
        return;
      }
    }

    // Fallback: Check if we have an organization ID in localStorage
    const storedOrgId = localStorage.getItem('organizationId');
    if (storedOrgId) {
      this.orgId = storedOrgId;
      console.log(`Organization ID from localStorage: ${this.orgId}`);
      return;
    }

    // If we still don't have an organization ID, show an error
    if (!this.orgId) {
      this.toastService.error(
        'Organization ID not available. Please log in again.'
      );
      console.error('Organization ID not available');
    }
  }

  /**
   * Fetch schedule data from Firebase
   * @param scheduleId The ID of the schedule to fetch
   */
  private fetchScheduleFromFirebase(scheduleId: string): void {
    if (!this.orgId) {
      this.toastService.error('Organization ID not available');
      this.isLoading = false;
      return;
    }

    // Construct the path to the schedule document
    const path = `organizations/${this.orgId}/schedules/${scheduleId}`;
    console.log(`Fetching schedule from Firebase: ${path}`);

    this.scheduleSubscription = this.firestoreService
      .getDocumentByPath<Schedule>(path)
      .subscribe({
        next: (schedule) => {
          if (schedule) {
            console.log('Schedule fetched from Firebase:', schedule);

            // Ensure the schedule has an ID property
            const scheduleWithId = {
              ...schedule,
              id: scheduleId
            };

            this.setScheduleDetails(scheduleWithId);

            // Now fetch the schedule results from the results subcollection
            this.fetchScheduleResults(scheduleId);
          } else {
            console.error('Schedule not found in Firebase');
            this.toastService.error('Schedule not found');
            this.isLoading = false;
          }
        },
        error: (err) => {
          console.error('Error fetching schedule from Firebase:', err);
          this.toastService.error('Failed to load schedule details');
          this.isLoading = false;
        },
      });
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }

    if (this.scheduleSubscription) {
      this.scheduleSubscription.unsubscribe();
    }

    if (this.resultsSubscription) {
      this.resultsSubscription.unsubscribe();
    }

    // Clear the schedule data when leaving the component
    this.scheduleDataService.clearCurrentSchedule();
  }

  /**
   * Fetch schedule results from the results subcollection
   * @param scheduleId The ID of the schedule to fetch results for
   */
  private fetchScheduleResults(scheduleId: string): void {
    if (!this.orgId) {
      this.toastService.error('Organization ID not available');
      this.isLoading = false;
      return;
    }

    // Construct the path to the results subcollection
    const path = `organizations/${this.orgId}/schedules/${scheduleId}/results`;
    console.log(`Fetching schedule results from Firebase: ${path}`);

    this.resultsSubscription = this.firestoreService
      .getCollection<any>(path)
      .subscribe({
        next: (results) => {
          console.log(
            `Found ${results.length} results for schedule ${scheduleId}`
          );

          if (results.length === 0) {
            // No results found
            this.participants = [];
            this.isLoading = false;
            return;
          }

          // Use the results directly without mapping to a specific interface
          this.participants = results;

          // Detect the event type from the results
          this.detectEventType();

          // Apply the default sorting (score by default)
          this.sortParticipants();

          this.isLoading = false;
        },
        error: (err) => {
          console.error('Error fetching schedule results:', err);
          this.toastService.error('Failed to load schedule results');
          this.isLoading = false;
        },
      });
  }

  /**
   * Set schedule details from passed data
   * @param schedule The schedule data
   */
  private setScheduleDetails(schedule: Schedule): void {
    console.log('Setting schedule details from passed data:', schedule);

    // Store the full schedule object
    this.schedule = schedule;

    // Also set individual properties for backward compatibility
    this.scheduleTitle = schedule.title || 'Untitled Schedule';
    this.scheduleType = schedule.type || 'Unknown Type';
    this.scheduleCategory = schedule.category || 'Uncategorized';
    this.scheduleDifficulty = schedule.difficulty || 'Medium';

    // Set the event type based on the schedule type
    this.eventType = (schedule.type as any) || 'unknown';
    console.log(`Event type set to: ${this.eventType}`);

    // Load generated questions if they exist in the schedule
    if ((schedule as any).generatedQuestions && Array.isArray((schedule as any).generatedQuestions)) {
      this.generatedQuestions = (schedule as any).generatedQuestions;
      console.log(`Loaded ${this.generatedQuestions.length} generated questions from schedule`);
    } else {
      this.generatedQuestions = [];
      console.log('No generated questions found in schedule');
    }

    // Load selected question indices if they exist
    if ((schedule as any).selectedQuestionIndices && Array.isArray((schedule as any).selectedQuestionIndices)) {
      this.selectedQuestionIndices = (schedule as any).selectedQuestionIndices;
      console.log(`Loaded ${this.selectedQuestionIndices.length} selected question indices from schedule`);
    } else {
      this.selectedQuestionIndices = [];
      console.log('No selected question indices found in schedule');
    }

    // Set the header title
    this.headerService.setPageTitle(this.scheduleTitle);
  }

  onParticipantClick(participant: Participant): void {
    console.log('Participant clicked:', participant);

    // Get the participant ID (could be in different properties depending on the event type)
    const participantId =
      participant.studentId || participant.id || participant.userId;

    if (!participantId) {
      console.error('Participant ID not found:', participant);
      this.toastService.error('Participant ID not found');
      return;
    }

    // For assessment or challenge type, navigate to the new participant details component
    if (this.eventType === 'assessment' || this.eventType === 'challenge') {
      this.router.navigate(
        ['/schedule', this.scheduleId, 'details', participantId],
        {
          queryParams: {
            type: this.eventType,
            batchId: participant.batchId || '',
            orgId: this.orgId,
          },
        }
      );
    }
    // For interview type, navigate to the participant details component with interview type
    else if (this.eventType === 'interview') {
      this.router.navigate(
        ['/schedule', this.scheduleId, 'details', participantId],
        {
          queryParams: {
            type: 'interview',
            batchId: participant.batchId || '',
            orgId: this.orgId,
          },
        }
      );
      console.log(
        'Navigating to interview details for participant:',
        participantId
      );
    }
    // For other types, use the existing participant performance component
    else {
      this.router.navigate([
        '/schedule',
        this.scheduleId,
        'participant',
        participantId,
      ]);
    }
  }

  /**
   * Calculate the average score of all participants
   * @returns The average score as a number
   */
  getAverageScore(): number {
    if (this.participants.length === 0) {
      return 0;
    }

    // Calculate the sum of all scores using the getScore helper method
    const totalScore = this.participants.reduce((sum, participant) => {
      return sum + this.getScore(participant);
    }, 0);

    // Return the average
    return totalScore / this.participants.length;
  }

  /**
   * Count the number of top performers (score >= 80%)
   * @returns The count of top performers
   */
  getTopPerformersCount(): number {
    if (this.participants.length === 0) {
      return 0;
    }

    // Count participants with score >= 80% using the getScore helper method
    return this.participants.filter(
      (participant) => this.getScore(participant) >= 80
    ).length;
  }

  /**
   * Get the score value from a participant, handling different schedule types
   * @param participant The participant object
   * @returns The score as a number
   */
  getScore(participant: any): number {
    // Handle assessment type (uses percentage instead of score)
    if (participant.percentage !== undefined) {
      return participant.percentage;
    }

    // Handle challenge type with xpEarned
    if (participant.xpEarned !== undefined) {
      // For challenges, we'll convert xpEarned to a percentage score
      // If correct is true, give 100%, otherwise calculate based on xpEarned
      if (participant.correct === true) {
        return 100;
      } else {
        // For challenges without a specific score, we'll use a binary approach
        // If there's any xpEarned, give 50%, otherwise 0%
        return participant.xpEarned > 0 ? 50 : 0;
      }
    }

    // Handle old challenge type
    if (participant.challengeScore !== undefined) {
      return participant.challengeScore;
    }

    // Handle interview/default type
    return participant.score || 0;
  }

  /**
   * Get the duration/time taken from a participant, handling different schedule types
   * @param participant The participant object
   * @returns The duration as a string
   */
  getDuration(participant: any): string {
    // Handle assessment type (uses duration)
    if (participant.duration) {
      return participant.duration;
    }

    // Handle challenge type (show attempts)
    if (participant.attempts !== undefined) {
      return `${participant.attempts} attempt${
        participant.attempts !== 1 ? 's' : ''
      }`;
    }

    // Handle interview/default type
    return participant.timeTaken || 'N/A';
  }

  /**
   * Get the completion date from a participant, handling different schedule types
   * @param participant The participant object
   * @returns The completion date as a number (timestamp)
   */
  getCompletionDate(participant: any): number | null {
    // Try different properties based on schedule type
    return participant.completedAt || participant.endTime || null;
  }

  /**
   * Get the batch ID from a participant, handling different schedule types
   * @param participant The participant object
   * @returns The batch ID as a string
   */
  getBatchId(participant: any): string {
    return participant.batchId || '-';
  }

  /**
   * Detect the event type from the schedule and results
   */
  private detectEventType(): void {
    // First try to detect from the schedule object
    if (this.schedule && this.schedule.type) {
      this.eventType = this.schedule.type as any;
      console.log(`Event type detected from schedule: ${this.eventType}`);
      return;
    }

    // If no schedule type, try to detect from the first result
    if (this.participants.length > 0) {
      const firstParticipant = this.participants[0];

      // Check for assessment type (has percentage instead of score)
      if (firstParticipant.percentage !== undefined) {
        this.eventType = 'assessment';
        console.log('Event type detected from results: assessment');
        return;
      }

      // Check for challenge type
      if (firstParticipant.challengeScore !== undefined) {
        this.eventType = 'challenge';
        console.log('Event type detected from results: challenge');
        return;
      }

      // Default to interview type if it has score
      if (firstParticipant.score !== undefined) {
        this.eventType = 'interview';
        console.log('Event type detected from results: interview');
        return;
      }
    }

    // Default to unknown if we couldn't detect
    this.eventType = 'unknown';
    console.log('Could not detect event type, using default');
  }

  /**
   * Get the result text based on the participant's score
   * @param participant The participant object
   * @returns The result text (Excellent, Good, Average, Needs Improvement)
   */
  getResultText(participant: any): string {
    // Special handling for challenge type
    if (this.eventType === 'challenge' && participant.correct !== undefined) {
      return participant.correct ? 'Correct' : 'Incorrect';
    }

    // For other types, use score-based result text
    const score = this.getScore(participant);

    if (score >= 90) return 'Excellent';
    if (score >= 80) return 'Good';
    if (score >= 70) return 'Average';
    if (score >= 60) return 'Fair';
    return 'Needs Improvement';
  }

  /**
   * Get the CSS class for the result badge based on the participant's score
   * @param participant The participant object
   * @returns The CSS class for styling the result badge
   */
  getResultClass(participant: any): string {
    // Special handling for challenge type
    if (this.eventType === 'challenge' && participant.correct !== undefined) {
      return participant.correct
        ? 'result-excellent'
        : 'result-needs-improvement';
    }

    // For other types, use score-based result class
    const score = this.getScore(participant);

    if (score >= 90) return 'result-excellent';
    if (score >= 80) return 'result-good';
    if (score >= 70) return 'result-average';
    if (score >= 60) return 'result-fair';
    return 'result-needs-improvement';
  }

  /**
   * Sort the participants by the specified column
   * @param column The column to sort by
   */
  sortBy(column: string): void {
    // If clicking the same column, toggle the sort direction
    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      // If clicking a different column, set it as the new sort column
      this.sortColumn = column;
      // Set default direction based on column type
      if (
        column === 'studentName' ||
        column === 'name' ||
        column === 'batchId'
      ) {
        this.sortDirection = 'asc'; // Alphabetical order for text columns
      } else {
        this.sortDirection = 'desc'; // Descending order for numeric columns (like score)
      }
    }

    // Apply the sorting
    this.sortParticipants();
  }

  /**
   * Sort the participants array based on the current sort column and direction
   */
  private sortParticipants(): void {
    this.participants.sort((a, b) => {
      let valueA: any;
      let valueB: any;

      // Handle different property names that might exist in different event types
      switch (this.sortColumn) {
        case 'name':
          valueA = a.studentName || a.name || '';
          valueB = b.studentName || b.name || '';
          break;
        case 'batch':
          valueA = a.batchId || '';
          valueB = b.batchId || '';
          break;
        case 'completedAt':
          valueA = this.getCompletionDate(a) || 0;
          valueB = this.getCompletionDate(b) || 0;
          break;
        case 'timeTaken':
          if (this.eventType === 'challenge') {
            // For challenge type, sort by attempts
            valueA = a.attempts || 0;
            valueB = b.attempts || 0;
          } else {
            // Convert time strings to minutes for comparison
            valueA = this.timeToMinutes(a.timeTaken || '0');
            valueB = this.timeToMinutes(b.timeTaken || '0');
          }
          break;
        case 'score':
          valueA = this.getScore(a);
          valueB = this.getScore(b);
          break;
        case 'result':
          // Convert result text to numeric value for sorting
          valueA = this.resultToValue(a);
          valueB = this.resultToValue(b);
          break;
        default:
          valueA = a[this.sortColumn] || '';
          valueB = b[this.sortColumn] || '';
      }

      // Compare the values based on sort direction
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return this.sortDirection === 'asc'
          ? valueA.localeCompare(valueB)
          : valueB.localeCompare(valueA);
      } else {
        return this.sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
      }
    });
  }

  /**
   * Convert time string (e.g., "5m 30s") to minutes for comparison
   * @param timeStr The time string to convert
   * @returns The time in minutes
   */
  private timeToMinutes(timeStr: string): number {
    if (!timeStr || timeStr === 'N/A') return 0;

    let minutes = 0;

    // Extract minutes
    const minMatch = timeStr.match(/(\d+)m/);
    if (minMatch && minMatch[1]) {
      minutes += parseInt(minMatch[1], 10);
    }

    // Extract seconds and convert to fraction of a minute
    const secMatch = timeStr.match(/(\d+)s/);
    if (secMatch && secMatch[1]) {
      minutes += parseInt(secMatch[1], 10) / 60;
    }

    return minutes;
  }

  /**
   * Convert result text to numeric value for sorting
   * @param participant The participant object
   * @returns A numeric value representing the result (higher is better)
   */
  private resultToValue(participant: any): number {
    // Special handling for challenge type
    if (this.eventType === 'challenge' && participant.correct !== undefined) {
      return participant.correct ? 2 : 1; // Correct (2) sorts higher than Incorrect (1)
    }

    // For other types, use score-based result value
    const score = this.getScore(participant);

    if (score >= 90) return 5; // Excellent
    if (score >= 80) return 4; // Good
    if (score >= 70) return 3; // Average
    if (score >= 60) return 2; // Fair
    return 1; // Needs Improvement
  }

  /**
   * Tab Management Methods
   */
  setActiveTab(tab: 'questions' | 'results' | 'settings'): void {
    // Auto-save before switching tabs if there are unsaved changes
    if (this.activeTab === 'questions' && this.hasUnsavedChanges()) {
      this.autoSaveBeforeTabSwitch();
    }

    this.activeTab = tab;
  }

  /**
   * Check if there are unsaved changes
   */
  private hasUnsavedChanges(): boolean {
    // Check if there are generated questions that haven't been saved
    if (this.generatedQuestions.length > 0) {
      // For assessment type, check if questions exist but not published
      if (this.schedule?.type === 'assessment') {
        return !(this.schedule as any)?.published;
      }

      // For other types, check if there are questions but not saved as draft
      return true; // Always save for safety
    }

    return false;
  }

  /**
   * Auto-save before switching tabs
   */
  private autoSaveBeforeTabSwitch(): void {
    if (this.isSaving || this.isPublishing) {
      return; // Don't auto-save if already saving
    }

    console.log('Auto-saving before tab switch...');

    // Call the appropriate save method based on schedule type
    if (this.schedule?.type === 'assessment') {
      this.saveDraft();
    } else if (this.schedule?.type === 'interview' && this.interviewComponent) {
      // Call interview component's save draft method
      this.interviewComponent.saveDraft();
    } else if (this.schedule?.type === 'challenge' && this.challengeComponent) {
      // Call challenge component's save draft method
      this.challengeComponent.saveDraft();
    }
  }

  /**
   * Get all topic groups including single topics and multi-topic combinations
   * @returns Array of topic group objects with name and questions
   */
  getAllTopicGroups(): { name: string; questions: any[]; isMultiTopic: boolean }[] {
    const topicGroups: { name: string; questions: any[]; isMultiTopic: boolean }[] = [];

    // Get all unique single topics first
    const singleTopics = new Set<string>();
    this.generatedQuestions.forEach(question => {
      const topics = question.topicsCovered && question.topicsCovered.length > 0
        ? question.topicsCovered
        : ['General'];
      topics.forEach((topic: string) => singleTopics.add(topic));
    });

    // Sort single topics alphabetically (General at the end)
    const sortedSingleTopics = Array.from(singleTopics).sort((a, b) => {
      if (a === 'General' && b !== 'General') return 1;
      if (b === 'General' && a !== 'General') return -1;
      return a.localeCompare(b);
    });

    // Add single topic groups
    sortedSingleTopics.forEach(topic => {
      const questions = this.generatedQuestions.filter(question => {
        const topics = question.topicsCovered && question.topicsCovered.length > 0
          ? question.topicsCovered
          : ['General'];
        return topics.length === 1 && topics[0] === topic;
      });

      if (questions.length > 0) {
        topicGroups.push({
          name: topic,
          questions: questions,
          isMultiTopic: false
        });
      }
    });

    // Get all unique multi-topic combinations
    const multiTopicCombinations = new Set<string>();
    this.generatedQuestions.forEach(question => {
      const topics = question.topicsCovered && question.topicsCovered.length > 0
        ? question.topicsCovered
        : ['General'];

      if (topics.length > 1) {
        // Sort topics in the combination for consistent naming
        const sortedTopics = [...topics].sort((a, b) => {
          if (a === 'General' && b !== 'General') return 1;
          if (b === 'General' && a !== 'General') return -1;
          return a.localeCompare(b);
        });
        multiTopicCombinations.add(sortedTopics.join(' + '));
      }
    });

    // Add multi-topic groups
    Array.from(multiTopicCombinations).sort().forEach(combination => {
      const combinationTopics = combination.split(' + ');
      const questions = this.generatedQuestions.filter(question => {
        const topics = question.topicsCovered && question.topicsCovered.length > 0
          ? question.topicsCovered
          : ['General'];

        if (topics.length !== combinationTopics.length) return false;

        const sortedQuestionTopics = [...topics].sort((a, b) => {
          if (a === 'General' && b !== 'General') return 1;
          if (b === 'General' && a !== 'General') return -1;
          return a.localeCompare(b);
        });

        return sortedQuestionTopics.join(' + ') === combination;
      });

      if (questions.length > 0) {
        topicGroups.push({
          name: combination,
          questions: questions,
          isMultiTopic: true
        });
      }
    });

    return topicGroups;
  }



  /**
   * Get all unique topics from generated questions for display in tags
   * @returns Array of unique topic names
   */
  getQuestionTopicsForTags(): string[] {
    const allTopics = new Set<string>();

    this.generatedQuestions.forEach(question => {
      const topics = question.topicsCovered && question.topicsCovered.length > 0
        ? question.topicsCovered
        : ['General'];
      topics.forEach((topic: string) => allTopics.add(topic));
    });

    // Sort topics alphabetically (General at the end)
    return Array.from(allTopics).sort((a, b) => {
      if (a === 'General' && b !== 'General') return 1;
      if (b === 'General' && a !== 'General') return -1;
      return a.localeCompare(b);
    });
  }

  /**
   * AI Integration Methods
   */

  /**
   * Generate smart suggestions based on recently deleted topics only
   */
  generateSmartSuggestions(): void {
    this.smartSuggestions = [];

    // Check if we have reached the required question count
    const requiredQuestions = this.getRequiredQuestionsCount();
    const currentQuestions = this.generatedQuestions.length;

    // Only show suggestions if:
    // 1. There are recently deleted topics
    // 2. Current question count is less than required count
    if (this.recentlyDeletedTopics.length > 0 && currentQuestions < requiredQuestions) {
      // Create suggestions for each recently deleted topic
      this.recentlyDeletedTopics.forEach(topic => {
        this.smartSuggestions.push(`Generate 1 more ${topic} question`);
      });

      console.log('Generated suggestions for deleted topics:', this.smartSuggestions);
      console.log(`Current questions: ${currentQuestions}, Required: ${requiredQuestions}`);
    } else if (currentQuestions >= requiredQuestions) {
      console.log('Hiding suggestions - required question count reached');
      // Clear recently deleted topics since we have enough questions
      this.recentlyDeletedTopics = [];
    }
  }



  /**
   * Handle AI prompt submission
   */
  submitAIPrompt(): void {
    if (!this.aiPrompt.trim() || this.isGeneratingFromPrompt) {
      return;
    }

    // For interview schedules, delegate to the interview component
    if (this.schedule?.type === 'interview' && this.interviewComponent) {
      this.interviewComponent.generateQuestionsFromAIPrompt(this.aiPrompt);
      this.aiPrompt = ''; // Clear the prompt
      return;
    }

    // Check if we already have enough questions
    const requiredQuestions = this.getRequiredQuestionsCount();
    const currentQuestions = this.generatedQuestions.length;

    if (currentQuestions >= requiredQuestions) {
      this.toastService.warning(`You already have ${currentQuestions} questions. Required: ${requiredQuestions}`);
      return;
    }

    this.isGeneratingFromPrompt = true;

    // Parse the prompt to extract requirements
    const requirements = this.parsePromptRequirements(this.aiPrompt);

    // Check if there are any remaining questions to generate
    if (requirements.count <= 0) {
      this.toastService.info(`All ${requiredQuestions} questions have been generated. Schedule is complete.`);
      return;
    }

    // Generate questions using AI
    this.generateQuestionsFromPrompt(requirements)
      .then((newQuestions) => {
        if (newQuestions && newQuestions.length > 0) {
          // Transform questions to match our format
          const transformedQuestions = newQuestions.map((q: any) => ({
            id: q.id,
            question: q.question,
            code: q.code || '',
            language: q.language || '',
            options: q.options?.map((opt: string, optIndex: number) => ({
              text: opt,
              isCorrect: optIndex === q.answerIndex
            })) || [],
            explanation: q.explanation || '',
            difficulty: requirements.difficulty,
            type: 'Multiple Choice',
            topicsCovered: q.topicsCovered || [requirements.topic || 'General']
          }));

          // Add new questions to existing ones
          this.generatedQuestions = [...this.generatedQuestions, ...transformedQuestions];

          // Update suggestions
          this.generateSmartSuggestions();

          // Clear prompt
          this.aiPrompt = '';

          this.toastService.success(`Generated ${transformedQuestions.length} questions successfully!`);
        }
      })
      .catch((error) => {
        console.error('Error generating questions from prompt:', error);
        this.toastService.error('Failed to generate questions. Please try again.');
      })
      .finally(() => {
        this.isGeneratingFromPrompt = false;
      });
  }

  /**
   * Parse prompt to extract requirements
   */
  private parsePromptRequirements(prompt: string): any {
    // Calculate remaining questions needed based on schedule
    const totalRequired = this.schedule?.numberOfQuestions || 10;
    const currentCount = this.generatedQuestions.length;
    const remainingCount = Math.max(0, totalRequired - currentCount);

    const requirements: any = {
      count: remainingCount, // Use remaining questions, not user input
      topic: this.schedule?.testTopic || 'General Programming',
      difficulty: this.schedule?.difficulty || 'Medium',
      type: 'theoretical'
    };

    // Override difficulty only if explicitly mentioned in prompt
    if (prompt.toLowerCase().includes('hard') || prompt.toLowerCase().includes('difficult')) {
      requirements.difficulty = 'Hard';
    } else if (prompt.toLowerCase().includes('easy') || prompt.toLowerCase().includes('simple')) {
      requirements.difficulty = 'Easy';
    }

    // Determine question type based on schedule type
    const scheduleType = this.schedule?.type || 'assessment';
    if (scheduleType === 'interview') {
      requirements.type = 'theoretical';
    } else if (scheduleType === 'challenge') {
      requirements.type = 'coding';
    } else {
      // For assessments, check if user specifically wants coding questions
      if (prompt.toLowerCase().includes('coding') || prompt.toLowerCase().includes('code')) {
        requirements.type = 'coding';
      } else {
        requirements.type = 'mix';
      }
    }

    return requirements;
  }

  /**
   * Generate questions from parsed requirements
   */
  private generateQuestionsFromPrompt(requirements: any): Promise<any[]> {
    return new Promise((resolve, reject) => {
      if (!this.schedule) {
        reject(new Error('Schedule data not available'));
        return;
      }

      // Create enhanced prompt for AI
      const enhancedPrompt = this.createEnhancedPrompt(requirements);

      // Call AI service using the existing pattern
      this.aiService.getAiResponse(enhancedPrompt, this.getSystemInstruction()).subscribe({
        next: (response: any) => {
          try {
            console.log('AI Response from prompt:', response);

            // Parse the AI response - response.content is a JSON string
            let questionsData;
            if (typeof response.content === 'string') {
              questionsData = JSON.parse(response.content);
            } else {
              questionsData = response.content;
            }

            if (Array.isArray(questionsData)) {
              const questions = questionsData.map((q: any, index: number) => ({
                ...q,
                id: `prompt_${Date.now()}_${index}`
              }));
              resolve(questions);
            } else {
              reject(new Error('Invalid response format'));
            }
          } catch (parseError) {
            console.error('Error parsing AI response:', parseError);
            reject(new Error('Invalid AI response format'));
          }
        },
        error: (error) => {
          console.error('Error generating questions from prompt:', error);
          reject(new Error('Failed to generate questions'));
        }
      });
    });
  }

  /**
   * Get system instruction for AI
   */
  private getSystemInstruction(): string {
    return `You must return ONLY a valid JSON array of questions. No other text, no explanations, no markdown formatting.

STRICT OUTPUT FORMAT - Return exactly this structure:
[
    {
        "question": "question text (without code)",
        "code": "code snippet if applicable (empty string if no code)",
        "language": "programming language for syntax highlighting (e.g., javascript, python, java, etc.)",
        "options": ["option1", "option2", "option3", "option4"],
        "explanation": "correct answer explanation",
        "answerIndex": 0,
        "topicsCovered": ["topic"]
    }
]

CRITICAL REQUIREMENTS:
- Return ONLY the JSON array, nothing else
- Do not wrap in any object or add any text
- Do not use markdown code blocks
- Ensure valid JSON syntax
- Provide exactly the requested number of questions
- answerIndex must be 0-based integer (0, 1, 2, or 3)
- Avoid quotes within text fields

QUESTION TYPE GUIDELINES:
- For "theoretical": Focus on concepts, set "code" to empty string
- For "coding": Include code snippets in "code" field
- For "mix": Balance theoretical and coding questions

Generate technical interview-style multiple-choice questions based on the given requirements.`;
  }

  /**
   * Create enhanced prompt for AI based on requirements
   */
  private createEnhancedPrompt(requirements: any): string {
    // Get schedule context
    const skillName = this.schedule?.skillName || 'Programming';
    const topics = this.schedule?.testTopic || 'General Programming';
    const difficulty = this.schedule?.difficulty || 'Medium';
    const scheduleType = this.schedule?.type || 'assessment';

    // Determine question type based on schedule type
    let questionType = 'mix';
    if (scheduleType === 'interview') {
      questionType = 'theoretical';
    } else if (scheduleType === 'challenge') {
      questionType = 'coding';
    }

    // Build context only if there are no existing questions
    let contextInfo = '';
    if (this.generatedQuestions.length === 0) {
      contextInfo = `
Context:
- Skill: ${skillName}
- Topics: ${topics}
- Difficulty: ${difficulty}
- Question Type: ${questionType}
- Schedule Type: ${scheduleType}

`;
    }

    // Create simple, effective prompt using user's request + schedule context
    return `${contextInfo}Generate ${requirements.count} ${difficulty.toLowerCase()} level multiple-choice questions for ${skillName} focusing on ${topics}.

User Request: "${this.aiPrompt.trim()}"

Generate questions that are appropriate for ${scheduleType} scenarios and match the ${difficulty} difficulty level.`;
  }

  /**
   * Use suggestion as prompt
   */
  useSuggestion(suggestion: string): void {
    this.aiPrompt = suggestion;
  }



  /**
   * Get question count for a specific topic
   */
  getTopicQuestionCount(topic: string): number {
    return this.generatedQuestions.filter(question => {
      const topics = question.topicsCovered && question.topicsCovered.length > 0
        ? question.topicsCovered
        : ['General'];
      return topics.includes(topic);
    }).length;
  }

  /**
   * Question Management Methods
   */
  generateQuestions(): void {
    if (!this.schedule) {
      this.toastService.error('Schedule data not available');
      return;
    }

    this.isGenerating = true;
    this.startLoadingAnimation();

    // Create system instruction for AI
    const systemInstruction = `You must return ONLY a valid JSON array of questions. No other text, no explanations, no markdown formatting.

STRICT OUTPUT FORMAT - Return exactly this structure:
[
    {
        "question": "question text (without code)",
        "code": "code snippet if applicable (empty string if no code)",
        "language": "programming language for syntax highlighting (e.g., javascript, python, java, etc.)",
        "options": ["option1", "option2", "option3", "option4"],
        "explanation": "correct answer explanation",
        "answerIndex": 0,
        "topicsCovered": ["topic"]
    }
]

CRITICAL REQUIREMENTS:
- Return ONLY the JSON array, nothing else
- Do not wrap in any object or add any text
- Do not use markdown code blocks
- Ensure valid JSON syntax
- Provide exactly the requested number of questions
- answerIndex must be 0-based integer (0, 1, 2, or 3)
- Avoid quotes within text fields
- for topics always give main topic only don't givesub topics correct: topicsCovered : ["while", "for"], wrong: topicsCovered : ["while loop", "syntax"]

QUESTION TYPE GUIDELINES:
- For "theoretical": Focus on concepts, set "code" to empty string
- For "coding": Include code snippets in "code" field
- For "mix": Balance theoretical and coding questions

Generate technical interview-style multiple-choice questions based on the given requirements.`;

    // Create prompt based on schedule details
    const skillName = this.schedule.skillName;
    const topics = this.schedule.testTopic;
    const difficulty = this.schedule.difficulty;
    const numberOfQuestions = this.schedule.numberOfQuestions;
    const scheduleType = this.schedule.type;

    // Determine question type based on schedule type
    let questionType = 'mix';
    if (scheduleType === 'interview') {
      questionType = 'theoretical';
    } else if (scheduleType === 'challenge') {
      questionType = 'coding';
    }

    const prompt = `Generate ${numberOfQuestions} ${difficulty.toLowerCase()} level multiple-choice questions for ${skillName} focusing on ${topics}.

Question Type: ${questionType}
Skill: ${skillName}
Topics: ${topics}
Difficulty: ${difficulty}
Number of Questions: ${numberOfQuestions}

Generate questions that are appropriate for technical interviews and ${scheduleType} scenarios.`;

    // Call AI service
    this.aiService.getAiResponse(prompt, systemInstruction).subscribe({
      next: (response:any) => {
        try {
          console.log('AI Response:', response);
          console.log('Response content type:', typeof response.content);
          console.log('Response content:', response.content);

          // Parse the AI response - response.content is a JSON string
          let questionsData;
          if (typeof response.content === 'string') {
            console.log('Parsing JSON string...');
            questionsData = JSON.parse(response.content);
          } else {
            console.log('Using response content directly...');
            questionsData = response.content;
          }

          console.log('Parsed questions data:', questionsData);
          console.log('Is array?', Array.isArray(questionsData));

          if (Array.isArray(questionsData)) {
            // Transform AI response to our format
            this.generatedQuestions = questionsData.map((q: any, index: number) => ({
              id: `q_${Date.now()}_${index}`,
              question: q.question,
              code: q.code || '',
              language: q.language || '',
              options: q.options?.map((opt: string, optIndex: number) => ({
                text: opt,
                isCorrect: optIndex === q.answerIndex
              })) || [],
              explanation: q.explanation || '',
              difficulty: difficulty,
              type: 'Multiple Choice',
              topicsCovered: q.topicsCovered || [topics]
            }));

            this.isGenerating = false;
            this.stopLoadingAnimation();
            this.toastService.success(`Generated ${this.generatedQuestions.length} content items successfully`);
          } else {
            console.error('questionsData is not an array:', questionsData);
            throw new Error(`Invalid response format - expected array, got ${typeof questionsData}`);
          }
        } catch (error) {
          console.error('Error parsing AI response:', error);
          console.error('Full error details:', error);
          this.isGenerating = false;
          this.stopLoadingAnimation();
          this.toastService.error('Failed to parse AI response. Please try again.');
        }
      },
      error: (error) => {
        console.error('Error generating questions:', error);
        this.isGenerating = false;
        this.stopLoadingAnimation();
        this.toastService.error('Failed to generate questions. Please try again.');
      }
    });
  }



  saveDraft(): void {
    if (this.generatedQuestions.length === 0) {
      this.toastService.error('No content to save');
      return;
    }

    console.log('=== SAVE DRAFT DEBUG ===');
    console.log('Schedule object:', this.schedule);
    console.log('Schedule ID from object:', this.schedule?.id);
    console.log('Schedule ID from component property:', this.scheduleId);
    console.log('All schedule properties:', Object.keys(this.schedule || {}));

    // Use scheduleId from component property as fallback
    const scheduleId = this.schedule?.id || this.scheduleId;
    console.log('Final schedule ID to use:', scheduleId);

    if (!scheduleId) {
      this.toastService.error('Schedule information not available');
      console.error('Schedule or schedule ID is missing:', {
        schedule: this.schedule,
        scheduleIdFromObject: this.schedule?.id,
        scheduleIdFromProperty: this.scheduleId
      });
      return;
    }

    this.isSaving = true;

    // Get current user info
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    // Prepare questions data for saving
    const questionsData = this.generatedQuestions.map((q: any) => ({
      id: q.id,
      question: q.question,
      code: q.code || '',
      language: q.language || '',
      options: q.options.map((opt: any) => ({
        text: opt.text,
        isCorrect: opt.isCorrect
      })),
      explanation: q.explanation || '',
      difficulty: q.difficulty || 'Medium',
      type: q.type || 'Multiple Choice',
      topicsCovered: q.topicsCovered || []
    }));

    // Get organization ID from route or use default
    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';

    // Update the schedule document with generated questions as draft
    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      generatedQuestions: questionsData,
      selectedQuestionIndices: this.selectedQuestionIndices,
      published: false,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || '',
      questionsCount: questionsData.length
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isSaving = false;
        this.toastService.success(`Successfully saved ${questionsData.length} questions as draft`);

        // Update local schedule data
        if (this.schedule) {
          (this.schedule as any)['generatedQuestions'] = questionsData;
          (this.schedule as any)['published'] = false;
          (this.schedule as any)['lastUpdatedAt'] = currentTime;
          (this.schedule as any)['lastUpdatedBy'] = currentUser?.uid || '';
      
        }
      },
      error: (error: any) => {
        console.error('Error saving draft:', error);
        this.isSaving = false;
        this.toastService.error('Failed to save draft. Please try again.');
      }
    });
  }

  publishQuestions(): void {
    if (this.generatedQuestions.length === 0) {
      this.toastService.error('No content to publish');
      return;
    }

    // For assessment type, publish all questions (no selection required)
    // For other types, check if any questions are selected
    if (this.schedule?.type !== 'assessment' && this.selectedQuestionIndices.length === 0) {
      this.toastService.error('Please select at least one question to publish');
      return;
    }

    // Use scheduleId from component property as fallback
    const scheduleId = this.schedule?.id || this.scheduleId;

    if (!scheduleId) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isPublishing = true;

    // Get current user info
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    // For assessment type, publish all questions; for others, publish only selected
    const questionsToPublish = this.schedule?.type === 'assessment'
      ? this.generatedQuestions
      : this.selectedQuestionIndices.map(index => this.generatedQuestions[index]);

    // Prepare questions data for publishing
    const questionsData = questionsToPublish.map((q: any) => ({
      id: q.id,
      question: q.question,
      code: q.code || '',
      language: q.language || '',
      options: q.options.map((opt: any) => ({
        text: opt.text,
        isCorrect: opt.isCorrect
      })),
      explanation: q.explanation || '',
      difficulty: q.difficulty || 'Medium',
      type: q.type || 'Multiple Choice',
      topicsCovered: q.topicsCovered || []
    }));

    // Get organization ID from route or use default
    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';

    // Update the schedule document with published questions
    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      generatedQuestions: questionsData,
      published: true,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || '',
      questionsCount: questionsData.length
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isPublishing = false;

        // Update local state to show only published questions
        this.generatedQuestions = questionsToPublish;
        this.selectedQuestionIndices = questionsToPublish.map((_: any, index: number) => index); // Reset indices to 0,1,2...

        this.toastService.success(`Successfully published ${questionsData.length} questions`);

        // Update local schedule data
        if (this.schedule) {
          (this.schedule as any)['generatedQuestions'] = questionsData;
          (this.schedule as any)['published'] = true;
          (this.schedule as any)['lastUpdatedAt'] = currentTime;
          (this.schedule as any)['lastUpdatedBy'] = currentUser?.uid || '';
          (this.schedule as any)['questionsCount'] = questionsData.length;
        }
      },
      error: (error: any) => {
        console.error('Error publishing questions:', error);
        this.isPublishing = false;
        this.toastService.error('Failed to publish questions. Please try again.');
      }
    });
  }

  /**
   * Toggle question selection
   */
  toggleQuestionSelection(index: number): void {
    const currentIndex = this.selectedQuestionIndices.indexOf(index);
    if (currentIndex > -1) {
      this.selectedQuestionIndices.splice(currentIndex, 1);
    } else {
      this.selectedQuestionIndices.push(index);
    }
  }

  /**
   * Check if question is selected
   */
  isQuestionSelected(index: number): boolean {
    return this.selectedQuestionIndices.includes(index);
  }

  /**
   * Get total selected questions count
   */
  getTotalSelectedCount(): number {
    return this.selectedQuestionIndices.length;
  }

  deleteQuestion(questionId: string): void {
    // Find the question being deleted to get its topics
    const questionToDelete = this.generatedQuestions.find(q => q.id === questionId);
    const questionIndex = this.generatedQuestions.findIndex(q => q.id === questionId);

    if (questionToDelete) {
      // Get topics from the deleted question
      const deletedTopics = questionToDelete.topicsCovered && questionToDelete.topicsCovered.length > 0
        ? questionToDelete.topicsCovered
        : ['General'];

      // Add to recently deleted topics (avoid duplicates)
      deletedTopics.forEach((topic:any) => {
        if (!this.recentlyDeletedTopics.includes(topic)) {
          this.recentlyDeletedTopics.push(topic);
        }
      });

      // Clear suggestions after 30 seconds
      setTimeout(() => {
        this.recentlyDeletedTopics = [];
        this.generateSmartSuggestions();
      }, 30000);
    }

    // Remove the question
    this.generatedQuestions = this.generatedQuestions.filter(q => q.id !== questionId);

    // Update selected indices after deletion
    if (questionIndex > -1) {
      // Remove from selected indices if it was selected
      const selectedIndex = this.selectedQuestionIndices.indexOf(questionIndex);
      if (selectedIndex > -1) {
        this.selectedQuestionIndices.splice(selectedIndex, 1);
      }
      // Adjust indices for questions that come after the removed one
      this.selectedQuestionIndices = this.selectedQuestionIndices.map(i => i > questionIndex ? i - 1 : i);
    }

    // Check if this was the last question
    const isLastQuestion = this.generatedQuestions.length === 0;

    if (isLastQuestion) {
      // Auto-save the empty state to prevent showing old questions on refresh
      this.autoSaveEmptyState();
      // Clear recently deleted topics since there are no questions left
      this.recentlyDeletedTopics = [];
      // Clear selected indices
      this.selectedQuestionIndices = [];
    } else {
      // Regenerate smart suggestions after deletion
      this.generateSmartSuggestions();
    }

    this.toastService.success('Content deleted successfully');
  }

  /**
   * Auto-save empty state when all questions are deleted
   */
  private autoSaveEmptyState(): void {
    // Use scheduleId from component property as fallback
    const scheduleId = this.schedule?.id || this.scheduleId;

    if (!scheduleId) {
      return;
    }

    // Get organization ID from route or use default
    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';

    // Get current user info
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    // Update the schedule document to clear questions
    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      generatedQuestions: [],
      published: false,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || '',
      questionsCount: 0
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        console.log('Auto-saved empty state after deleting all questions');

        // Update local schedule data
        if (this.schedule) {
          (this.schedule as any)['generatedQuestions'] = [];
          (this.schedule as any)['published'] = false;
          (this.schedule as any)['lastUpdatedAt'] = currentTime;
          (this.schedule as any)['lastUpdatedBy'] = currentUser?.uid || '';
          (this.schedule as any)['questionsCount'] = 0;
        }
      },
      error: (error: any) => {
        console.error('Error auto-saving empty state:', error);
        // Don't show error to user as this is background operation
      }
    });
  }

  /**
   * Check if this schedule has ever had questions (to determine which empty state to show)
   */
  hasEverHadQuestions(): boolean {
    // Check if schedule has lastUpdatedAt or lastUpdatedBy (indicates questions were generated/saved before)
    const schedule = this.schedule as any;
    return !!(schedule?.lastUpdatedAt || schedule?.lastUpdatedBy || schedule?.questionsCount > 0);
  }

  /**
   * Get the required number of questions from schedule
   */
  getRequiredQuestionsCount(): number {
    return this.schedule?.numberOfQuestions || 10;
  }

  /**
   * Get the number of missing questions
   */
  getMissingQuestionsCount(): number {
    const required = this.getRequiredQuestionsCount();
    const current = this.generatedQuestions.length;
    return Math.max(0, required - current);
  }



  /**
   * Get dynamic button text based on schedule type
   */
  getGenerateButtonText(): string {
    const scheduleType = this.schedule?.type || 'assessment';
    switch (scheduleType.toLowerCase()) {
      case 'assessment':
        return 'Generate Questions';
      case 'challenge':
        return 'Generate Challenge';
      case 'interview':
        return 'Generate Interview Questions';
      default:
        return 'Generate Content';
    }
  }



  /**
   * Get dynamic empty state title based on schedule type
   */
  getEmptyStateTitle(): string {
    const scheduleType = this.schedule?.type || 'assessment';
    switch (scheduleType.toLowerCase()) {
      case 'assessment':
        return 'No Questions Generated';
      case 'challenge':
        return 'No Challenge Generated';
      case 'interview':
        return 'No Interview Questions Generated';
      default:
        return 'No Content Generated';
    }
  }

  /**
   * PDF Upload Methods
   */
  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.handleFileSelection(files[0]);
    }
  }

  triggerFileInput(): void {
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }

  onFileSelected(event: Event): void {
    const target = event.target as HTMLInputElement;
    const files = target.files;
    if (files && files.length > 0) {
      this.handleFileSelection(files[0]);
    }
  }

  private handleFileSelection(file: File): void {
    // Validate file type
    if (file.type !== 'application/pdf') {
      this.toastService.error('Please select a PDF file');
      return;
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      this.toastService.error('File size must be less than 10MB');
      return;
    }

    this.selectedFile = file;
    console.log('PDF file selected:', file.name, 'Size:', this.formatFileSize(file.size));
  }

  removeFile(event: Event): void {
    event.stopPropagation();
    this.selectedFile = null;
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Generate questions from uploaded PDF
   */
  generateQuestionsFromPDF(): void {
    if (!this.selectedFile) {
      this.toastService.error('No PDF file selected');
      return;
    }

    if (!this.schedule) {
      this.toastService.error('Schedule data not available');
      return;
    }

    this.isGeneratingFromPDF = true;

    // Convert file to base64
    const reader = new FileReader();
    reader.onload = () => {
      const base64Data = reader.result as string;
      // Remove the data URL prefix to get just the base64 data
      const base64Content = base64Data.split(',')[1];

      this.sendPDFToAI(base64Content);
    };

    reader.onerror = () => {
      this.isGeneratingFromPDF = false;
      this.toastService.error('Failed to read PDF file');
    };

    reader.readAsDataURL(this.selectedFile);
  }

  private sendPDFToAI(base64Content: string): void {
    // Create enhanced prompt for PDF-based question generation
    const skillName = this.schedule?.skillName || 'Programming';
    const topics = this.schedule?.testTopic || 'General Programming';
    const difficulty = this.schedule?.difficulty || 'Medium';
    const questionCount = this.schedule?.numberOfQuestions || 10;
    const scheduleType = this.schedule?.type || 'assessment';

    // Determine question type based on schedule type
    let questionType = 'mix';
    if (scheduleType === 'interview') {
      questionType = 'theoretical';
    } else if (scheduleType === 'challenge') {
      questionType = 'coding';
    }

    const prompt = `Based on the content of this PDF document, generate ${questionCount} ${difficulty.toLowerCase()} level multiple-choice questions for a ${scheduleType}.

Context:
- Skill: ${skillName}
- Topics: ${topics}
- Difficulty: ${difficulty}
- Question Type: ${questionType}
- Number of Questions: ${questionCount}

Instructions:
1. Analyze the PDF content thoroughly
2. Extract key concepts, principles, and technical details
3. Create questions that test understanding of the material
4. Ensure questions are appropriate for ${scheduleType} scenarios
5. Focus on practical application and conceptual understanding

Generate questions that cover the main topics and concepts from the PDF document.`;

    // Create the system instruction
    const systemInstruction = this.getSystemInstruction();

    // Call AI service with PDF content
    this.aiService.getAiResponseWithPDF(prompt, systemInstruction, base64Content).subscribe({
      next: (response: any) => {
        try {
          console.log('AI Response from PDF:', response);

          // Parse the AI response - response.content is a JSON string
          let questionsData;
          if (typeof response.content === 'string') {
            questionsData = JSON.parse(response.content);
          } else {
            questionsData = response.content;
          }

          if (Array.isArray(questionsData)) {
            // Transform AI response to our format
            const newQuestions = questionsData.map((q: any, index: number) => ({
              id: `pdf_${Date.now()}_${index}`,
              question: q.question,
              code: q.code || '',
              language: q.language || '',
              options: q.options?.map((opt: string, optIndex: number) => ({
                text: opt,
                isCorrect: optIndex === q.answerIndex
              })) || [],
              explanation: q.explanation || '',
              difficulty: difficulty,
              type: 'Multiple Choice',
              topicsCovered: q.topicsCovered || [topics]
            }));

            // Set the generated questions
            this.generatedQuestions = newQuestions;

            // Clear the selected file
            this.selectedFile = null;

            this.isGeneratingFromPDF = false;
            this.toastService.success(`Generated ${newQuestions.length} questions from PDF successfully!`);
          } else {
            throw new Error('Invalid response format');
          }
        } catch (error) {
          console.error('Error parsing AI response from PDF:', error);
          this.isGeneratingFromPDF = false;
          this.toastService.error('Failed to parse AI response. Please try again.');
        }
      },
      error: (error: any) => {
        console.error('Error generating questions from PDF:', error);
        this.isGeneratingFromPDF = false;
        this.toastService.error('Failed to generate questions from PDF. Please try again.');
      }
    });
  }





  /**
   * Get completion rate percentage
   * @returns Completion rate as percentage
   */
  getCompletionRate(): number {
    const totalAssigned = this.getTotalAssigned();
    if (totalAssigned === 0) return 0;
    return Math.round((this.participants.length / totalAssigned) * 100);
  }

  /**
   * Get total assigned participants (estimated based on batch assignments)
   * @returns Total assigned count
   */
  getTotalAssigned(): number {
    // For now, return participants count + 20% as estimated total
    // This can be improved with actual batch enrollment data
    return Math.max(this.participants.length, Math.ceil(this.participants.length * 1.2));
  }

  /**
   * Get highest score among participants
   * @returns Highest score
   */
  getHighestScore(): number {
    if (this.participants.length === 0) return 0;
    return Math.max(...this.participants.map(p => this.getScore(p)));
  }

  /**
   * Get lowest score among participants
   * @returns Lowest score
   */
  getLowestScore(): number {
    if (this.participants.length === 0) return 0;
    return Math.min(...this.participants.map(p => this.getScore(p)));
  }

  /**
   * Get performance count by category
   * @param category Performance category
   * @returns Count of participants in that category
   */
  getPerformanceCount(category: string): number {
    return this.participants.filter(p => {
      const score = this.getScore(p);
      switch (category) {
        case 'excellent': return score >= 90;
        case 'good': return score >= 70 && score < 90;
        case 'average': return score >= 50 && score < 70;
        case 'poor': return score < 50;
        default: return false;
      }
    }).length;
  }

  /**
   * Get average time taken
   * @returns Average time as string
   */
  getAverageTime(): string {
    if (this.participants.length === 0) return '0 min';

    const totalMinutes = this.participants.reduce((sum, p) => {
      const duration = this.getDuration(p);
      const minutes = this.parseTimeToMinutes(duration);
      return sum + minutes;
    }, 0);

    const avgMinutes = Math.round(totalMinutes / this.participants.length);
    return this.formatMinutesToTime(avgMinutes);
  }

  /**
   * Get fastest completion time
   * @returns Fastest time as string
   */
  getFastestTime(): string {
    if (this.participants.length === 0) return '0 min';

    const times = this.participants.map(p => this.parseTimeToMinutes(this.getDuration(p)));
    const fastest = Math.min(...times);
    return this.formatMinutesToTime(fastest);
  }

  /**
   * Get slowest completion time
   * @returns Slowest time as string
   */
  getSlowestTime(): string {
    if (this.participants.length === 0) return '0 min';

    const times = this.participants.map(p => this.parseTimeToMinutes(this.getDuration(p)));
    const slowest = Math.max(...times);
    return this.formatMinutesToTime(slowest);
  }

  /**
   * Parse time string to minutes
   * @param timeStr Time string (e.g., "45 min", "1h 30m")
   * @returns Time in minutes
   */
  private parseTimeToMinutes(timeStr: string): number {
    if (!timeStr || timeStr === '-') return 0;

    // Handle different time formats
    const hourMatch = timeStr.match(/(\d+)h/);
    const minuteMatch = timeStr.match(/(\d+)m/);
    const simpleMinuteMatch = timeStr.match(/(\d+)\s*min/);

    let totalMinutes = 0;

    if (hourMatch) {
      totalMinutes += parseInt(hourMatch[1]) * 60;
    }

    if (minuteMatch) {
      totalMinutes += parseInt(minuteMatch[1]);
    } else if (simpleMinuteMatch) {
      totalMinutes += parseInt(simpleMinuteMatch[1]);
    }

    return totalMinutes || 0;
  }

  /**
   * Format minutes to readable time string
   * @param minutes Time in minutes
   * @returns Formatted time string
   */
  private formatMinutesToTime(minutes: number): string {
    if (minutes < 60) {
      return `${minutes} min`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
    }
  }

  /**
   * Get the sort icon class based on the column and current sort state
   * @param column The column to check
   * @returns The appropriate icon class
   */
  getSortIconClass(column: string): string {
    if (this.sortColumn !== column) {
      return 'fa-sort'; // Default unsorted icon
    }

    // Use standard Font Awesome icons that are definitely available
    return this.sortDirection === 'asc' ? 'fa-sort-up' : 'fa-sort-down';
  }

  /**
   * Download the results as an Excel file
   */
  downloadResults(): void {
    if (this.participants.length === 0) {
      this.toastService.error('No data to download');
      return;
    }

    try {
      // Create a workbook with a worksheet
      const workbook = this.createExcelWorkbook();

      // Generate filename based on schedule title and date
      const fileName = this.generateFileName();

      // Save the file
      workbook.xlsx.writeBuffer().then((buffer: ArrayBuffer) => {
        const blob = new Blob([buffer], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        a.click();
        window.URL.revokeObjectURL(url);

        this.toastService.success('Results downloaded successfully');
      });
    } catch (error) {
      console.error('Error downloading results:', error);
      this.toastService.error('Failed to download results');
    }
  }

  /**
   * Create an Excel workbook with the results data
   * @returns An ExcelJS workbook
   */
  private createExcelWorkbook(): ExcelJS.Workbook {
    // Create a new workbook
    const workbook = new ExcelJS.Workbook();

    // Add metadata
    workbook.creator = 'Olearn Admin Dashboard';
    workbook.lastModifiedBy = 'Olearn Admin Dashboard';
    workbook.created = new Date();
    workbook.modified = new Date();

    // Add a worksheet
    const worksheet = workbook.addWorksheet('Results');

    // Define columns based on event type
    if (this.eventType === 'assessment') {
      worksheet.columns = [
        { header: 'Name', key: 'name', width: 30 },
        { header: 'Batch', key: 'batch', width: 15 },
        { header: 'Completed At', key: 'completedAt', width: 20 },
        { header: 'Duration', key: 'duration', width: 15 },
        { header: 'Percentage', key: 'percentage', width: 10 },
        { header: 'Result', key: 'result', width: 20 },
      ];
    } else if (this.eventType === 'challenge') {
      worksheet.columns = [
        { header: 'Name', key: 'name', width: 30 },
        { header: 'Batch', key: 'batch', width: 15 },
        { header: 'Completed At', key: 'completedAt', width: 20 },
        { header: 'Attempts', key: 'attempts', width: 10 },
        { header: 'XP Earned', key: 'xpEarned', width: 10 },
        { header: 'Result', key: 'result', width: 20 },
      ];
    } else {
      worksheet.columns = [
        { header: 'Name', key: 'name', width: 30 },
        { header: 'Batch', key: 'batch', width: 15 },
        { header: 'Completed At', key: 'completedAt', width: 20 },
        { header: 'Time Taken', key: 'timeTaken', width: 15 },
        { header: 'Score', key: 'score', width: 10 },
        { header: 'Result', key: 'result', width: 20 },
      ];
    }

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4F4F4F' },
    };
    headerRow.font = { color: { argb: 'FFFFFF' }, bold: true };

    // Add data rows based on event type
    this.participants.forEach((participant) => {
      if (this.eventType === 'assessment') {
        worksheet.addRow({
          name: participant.studentName || participant.name || 'Unknown',
          batch: participant.batchId ? `Batch ${participant.batchId}` : '-',
          completedAt: this.formatDate(participant.completedAt),
          duration: participant.duration || 'N/A',
          percentage: participant.percentage || 0,
          result: this.getResultText(participant),
        });
      } else if (this.eventType === 'challenge') {
        worksheet.addRow({
          name: participant.studentName || participant.name || 'Unknown',
          batch: participant.batchId ? `Batch ${participant.batchId}` : '-',
          completedAt: this.formatDate(participant.completedAt),
          attempts: participant.attempts || 1,
          xpEarned: participant.xpEarned || 0,
          result: this.getResultText(participant),
        });
      } else {
        worksheet.addRow({
          name: participant.studentName || participant.name || 'Unknown',
          batch: participant.batchId ? `Batch ${participant.batchId}` : '-',
          completedAt: this.formatDate(
            participant.completedAt || participant.endTime
          ),
          timeTaken: participant.timeTaken || 'N/A',
          score: participant.score || 0,
          result: this.getResultText(participant),
        });
      }
    });

    // Style the data rows
    for (let i = 2; i <= worksheet.rowCount; i++) {
      const row = worksheet.getRow(i);

      // Add alternating row colors
      if (i % 2 === 0) {
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'F9F9F9' },
        };
      }

      // Style the score/percentage/result cell based on the event type and value
      if (this.eventType === 'assessment') {
        // For assessment type, style the percentage cell (column 5)
        const scoreCell = row.getCell(5);
        const score = Number(scoreCell.value);
        this.styleScoreCell(scoreCell, score);
      } else if (this.eventType === 'challenge') {
        // For challenge type, style the result cell (column 6)
        const resultCell = row.getCell(6);
        const resultText = String(resultCell.value);

        if (resultText === 'Correct') {
          resultCell.font = { color: { argb: '48BB78' } }; // Green
        } else {
          resultCell.font = { color: { argb: 'F56565' } }; // Red
        }

        // Also style the XP Earned cell
        const xpCell = row.getCell(5);
        if (Number(xpCell.value) > 0) {
          xpCell.font = { color: { argb: '4299E1' } }; // Blue
        }
      } else {
        // For other types, style the score cell (column 5)
        const scoreCell = row.getCell(5);
        const score = Number(scoreCell.value);
        this.styleScoreCell(scoreCell, score);
      }
    }

    // Add a title above the table
    worksheet.insertRow(1, [this.scheduleTitle]);
    const titleRow = worksheet.getRow(1);
    titleRow.font = { size: 16, bold: true };
    titleRow.height = 30;

    // Merge cells for the title
    worksheet.mergeCells('A1:F1');

    // Add schedule details
    const detailsRow = worksheet.insertRow(2, [
      `Type: ${this.scheduleType} | Difficulty: ${
        this.scheduleDifficulty
      } | Date: ${new Date().toLocaleDateString()}`,
    ]);
    detailsRow.font = { size: 12, italic: true };

    // Merge cells for the details
    worksheet.mergeCells('A2:F2');

    // Add some space before the table
    worksheet.insertRow(3, []);

    return workbook;
  }

  /**
   * Format a date for Excel
   * @param timestamp The timestamp to format
   * @returns A formatted date string
   */
  private formatDate(timestamp: number | undefined): string {
    if (!timestamp) return '-';

    const date = new Date(timestamp);
    return date.toLocaleString();
  }

  /**
   * Style a score cell in Excel based on the score value
   * @param cell The Excel cell to style
   * @param score The score value
   */
  private styleScoreCell(cell: any, score: number): void {
    if (score >= 90) {
      cell.font = { color: { argb: '48BB78' } }; // Green
    } else if (score >= 80) {
      cell.font = { color: { argb: '4299E1' } }; // Blue
    } else if (score >= 70) {
      cell.font = { color: { argb: 'ECC94B' } }; // Yellow
    } else if (score >= 60) {
      cell.font = { color: { argb: 'ED8936' } }; // Orange
    } else {
      cell.font = { color: { argb: 'F56565' } }; // Red
    }
  }

  /**
   * Generate a filename for the Excel file
   * @returns A filename string
   */
  private generateFileName(): string {
    const date = new Date();
    const formattedDate = date.toISOString().split('T')[0]; // YYYY-MM-DD
    const sanitizedTitle = (this.scheduleTitle || 'Schedule')
      .replace(/[^a-z0-9]/gi, '_') // Replace non-alphanumeric with underscore
      .toLowerCase();

    return `${sanitizedTitle}_results_${formattedDate}.xlsx`;
  }

  /**
   * Navigate back to the previous page
   */
  goBack(): void {
    this.location.back();
  }

  /**
   * Loading Animation Methods
   */
  private startLoadingAnimation(): void {
    let messageIndex = 0;
    let charIndex = 0;
    let currentMessage = this.loadingMessages[messageIndex];

    this.currentLoadingText = '';

    // Type current message
    this.typingInterval = setInterval(() => {
      if (charIndex < currentMessage.length) {
        this.currentLoadingText += currentMessage.charAt(charIndex);
        charIndex++;
      } else {
        // Message complete, wait then move to next
        clearInterval(this.typingInterval);

        setTimeout(() => {
          messageIndex = (messageIndex + 1) % this.loadingMessages.length;
          currentMessage = this.loadingMessages[messageIndex];
          charIndex = 0;
          this.currentLoadingText = '';

          // Start typing next message
          this.typingInterval = setInterval(() => {
            if (charIndex < currentMessage.length) {
              this.currentLoadingText += currentMessage.charAt(charIndex);
              charIndex++;
            } else {
              // Restart cycle
              clearInterval(this.typingInterval);
              setTimeout(() => {
                if (this.isGenerating) {
                  this.startLoadingAnimation();
                }
              }, 1000);
            }
          }, 50);
        }, 1500);
      }
    }, 50);
  }

  private stopLoadingAnimation(): void {
    if (this.typingInterval) {
      clearInterval(this.typingInterval);
    }
    if (this.messageInterval) {
      clearInterval(this.messageInterval);
    }
    this.currentLoadingText = '';
  }

  /**
   * Question Expansion Methods
   */
  toggleQuestionExpansion(questionId: string): void {
    if (this.expandedQuestions.has(questionId)) {
      this.expandedQuestions.delete(questionId);
    } else {
      this.expandedQuestions.add(questionId);
    }
  }

  isQuestionExpanded(questionId: string): boolean {
    return this.expandedQuestions.has(questionId);
  }

  /**
   * Get option label (A, B, C, D)
   */
  getOptionLabel(index: number): string {
    return String.fromCharCode(65 + index) + ')';
  }
}
