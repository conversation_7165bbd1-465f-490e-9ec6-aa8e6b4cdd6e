<div class="schedule-detail-container">
  <div class="back-button-container">
    <button class="back-btn" (click)="goBack()">
      <i class="fa-solid fa-arrow-left"></i> <span>Back</span>
    </button>
  </div>

  <!-- Loading indicator -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner">
      <i class="fa-solid fa-circle-notch fa-spin"></i>
    </div>
    <p>Loading schedule details...</p>
  </div>

  <!-- Schedule content (only shown when not loading) -->
  <div *ngIf="!isLoading">
    <div class="schedule-header">
      <div class="schedule-info">
        <div class="title-meta-row">
          <h1 class="schedule-title">{{scheduleTitle}}</h1>
          <div class="schedule-meta">
            <div class="schedule-tag">{{scheduleType}}</div>
            <div class="difficulty-tag">{{scheduleDifficulty}}</div>
          </div>
        </div>

        <!-- Additional schedule details -->
        <div class="schedule-details" *ngIf="schedule">
          <div class="detail-item" *ngIf="schedule?.createdAt">
            <i class="fa-regular fa-calendar-plus"></i>
            <span>Created: {{schedule.createdAt | date:'MMM d, y'}}</span>
          </div>
          <div class="detail-item" *ngIf="schedule?.scheduleDate">
            <i class="fa-regular fa-calendar"></i>
            <span>Scheduled: {{schedule.scheduleDate | date:'MMM d, y'}}
              <span *ngIf="schedule?.time || schedule?.startTime">at {{schedule.startTime || schedule.time}}</span>
              <span *ngIf="schedule?.endTime"> - {{schedule.endTime}}</span>
              <span *ngIf="schedule?.['endDate']"> to {{schedule['endDate'] | date:'MMM d, y'}}</span>
            </span>
          </div>
          <div class="detail-item" *ngIf="schedule?.numberOfQuestions">
            <i class="fa-solid fa-list-ol"></i>
            <span>Questions: {{schedule.numberOfQuestions}}</span>
          </div>
          <div class="detail-item" *ngIf="schedule?.testDuration">
            <i class="fa-regular fa-clock"></i>
            <span>Duration: {{schedule.testDuration}}</span>
          </div>
          <div class="detail-item" *ngIf="schedule.assignedBatches?.length">
            <i class="fa-solid fa-users"></i>
            <span>Assigned Batches:
              <ng-container *ngFor="let batchId of schedule.assignedBatches; let last = last">
                {{batchId}}{{!last ? ', ' : ''}}
              </ng-container>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Schedule Tabs -->
    <div class="schedule-tabs">
      <div class="tab" [class.active]="activeTab === 'questions'" (click)="setActiveTab('questions')">
        Content
      </div>
      <div class="tab" [class.active]="activeTab === 'results'" (click)="setActiveTab('results')">
       Results
      </div>
      <div class="tab" [class.active]="activeTab === 'settings'" (click)="setActiveTab('settings')">
        info
      </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Management Tab -->
      <div class="tab-pane" *ngIf="activeTab === 'questions'">
        <div class="management-layout">
          <!-- Left Side: Content Management -->
          <div class="management-content-left">
            <div class="management-section">
              <div class="section-header">
                <h2 class="section-title">Content Management</h2>
                <div class="management-actions" *ngIf="generatedQuestions.length > 0">
                  <button class="action-btn save-draft-btn" (click)="saveDraft()"
                          [disabled]="isSaving || isPublishing">
                    <i class="fa-solid" [ngClass]="isSaving ? 'fa-spinner fa-spin' : 'fa-save'"></i>
                    {{isSaving ? 'Saving...' : 'Save Draft'}}
                  </button>
                  <button class="action-btn publish-btn" (click)="publishQuestions()"
                          [disabled]="isSaving || isPublishing">
                    <i class="fa-solid" [ngClass]="isPublishing ? 'fa-spinner fa-spin' : 'fa-paper-plane'"></i>
                    {{isPublishing ? 'Publishing...' : 'Publish'}}
                  </button>
                </div>
              </div>

            <!-- Missing Questions Indicator -->
            <div class="missing-questions-indicator" *ngIf="getMissingQuestionsCount() > 0 && generatedQuestions.length > 0">
              <div class="indicator-content">
                <div class="indicator-text">
                  <i class="fa-solid fa-exclamation-triangle"></i>
                  <span>{{getMissingQuestionsCount()}} {{getMissingQuestionsCount() === 1 ? 'question' : 'questions'}} missing from the required {{getRequiredQuestionsCount()}}</span>
                </div>
                <button class="action-btn generate-missing-btn" (click)="generateMissingQuestions()" [disabled]="isGenerating">
                  <i class="fa-solid" [ngClass]="isGenerating ? 'fa-spinner fa-spin' : 'fa-plus'"></i>
                  Generate {{getMissingQuestionsCount()}} Missing
                </button>
              </div>
            </div>

            <!-- Content List - Grouped by Topics -->
            <div class="content-list" *ngIf="generatedQuestions.length > 0">
              <!-- Topic Groups (Single Topics + Multi-Topic Combinations) -->
              <div class="topic-group"
                   *ngFor="let topicGroup of getAllTopicGroups(); let topicIndex = index"
                   [ngClass]="{'multi-topic-group': topicGroup.isMultiTopic}">
                <!-- Topic Header -->
                <div class="topic-header">
                  <div class="topic-title">
                    <i class="fa-solid" [ngClass]="topicGroup.isMultiTopic ? 'fa-layer-group' : 'fa-folder'"></i>
                    <span class="topic-name">{{topicGroup.name}}</span>
                    <span class="topic-count">({{topicGroup.questions.length}})</span>
                  </div>
                </div>

                <!-- Questions in this topic -->
                <div class="topic-questions">
                  <div class="content-item" *ngFor="let question of topicGroup.questions; let i = index">
                    <div class="content-header" (click)="toggleQuestionExpansion(question.id)">
                      <div class="content-details">
                        <div class="content-text">{{i + 1}}. {{question.question}}</div>
                        <div class="content-meta">
                          <span class="difficulty" *ngIf="question.difficulty && question.difficulty !== 'Easy'">{{question.difficulty}}</span>
                          <span class="expand-indicator">
                            <i class="fa-solid" [ngClass]="isQuestionExpanded(question.id) ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                          </span>
                        </div>
                      </div>
                      <div class="content-actions">
                        <button class="action-btn-small delete-btn" (click)="deleteQuestion(question.id); $event.stopPropagation()" title="Delete">
                          <i class="fa-solid fa-trash"></i>
                        </button>
                      </div>
                    </div>

                    <!-- Expanded Question Details -->
                    <div class="question-preview" *ngIf="isQuestionExpanded(question.id)">
                      <!-- Code Block (if present) -->
                      <div class="code-block" *ngIf="question.code">
                        <div class="code-header">
                          <span class="code-language">{{question.language || 'Code'}}</span>
                        </div>
                        <pre><code>{{question.code}}</code></pre>
                      </div>

                      <!-- Options -->
                      <div class="options-section">
                        <h4>Options:</h4>
                        <div class="options-list">
                          <div class="option-item"
                               *ngFor="let option of question.options; let optIndex = index"
                               [ngClass]="{'correct-option': option.isCorrect}">
                            <span class="option-label">{{getOptionLabel(optIndex)}}</span>
                            <span class="option-text">{{option.text}}</span>
                            <span class="correct-indicator" *ngIf="option.isCorrect">
                              <i class="fa-solid fa-check-circle"></i>
                            </span>
                          </div>
                        </div>
                      </div>

                      <!-- Explanation -->
                      <div class="explanation-section" *ngIf="question.explanation">
                        <h4>Explanation:</h4>
                        <p class="explanation-text">{{question.explanation}}</p>
                      </div>

                      <!-- Topics Covered -->
                      <div class="topics-section" *ngIf="question.topicsCovered && question.topicsCovered.length > 0">
                        <h4>Topics Covered:</h4>
                        <div class="topics-tags">
                          <span class="topic-tag" *ngFor="let topic of question.topicsCovered">{{topic}}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Loading State -->
            <div class="loading-state" *ngIf="generatedQuestions.length === 0 && isGenerating">
              <div class="loading-content">
                <div class="loading-icon">
                  <div class="ai-brain">
                    <div class="brain-pulse"></div>
                    <i class="fa-solid fa-brain"></i>
                  </div>
                </div>
                <div class="loading-text">
                  <span class="typing-text">{{currentLoadingText}}</span>
                  <span class="cursor">|</span>
                </div>
              </div>
            </div>

            <!-- Content Generation Options (Initial State Only) -->
            <div class="content-generation-options" *ngIf="generatedQuestions.length === 0 && !isGenerating && !hasEverHadQuestions()">
              <div class="generation-methods">

                <!-- PDF Upload Option -->
                <div class="generation-option">
                  <div class="option-header">
                    <i class="fa-solid fa-file-pdf"></i>
                    <h4>Upload PDF</h4>
                  </div>

                  <!-- PDF Upload Area -->
                  <div class="pdf-upload-area"
                       [class.dragover]="isDragOver"
                       (dragover)="onDragOver($event)"
                       (dragleave)="onDragLeave($event)"
                       (drop)="onDrop($event)"
                       (click)="triggerFileInput()">
                    <input #fileInput
                           type="file"
                           accept=".pdf"
                           (change)="onFileSelected($event)"
                           style="display: none;">

                    <div class="upload-content" *ngIf="!selectedFile">
                      <i class="fa-solid fa-cloud-upload-alt"></i>
                      <p class="upload-text">Drag & drop PDF here or click to browse</p>
                      <span class="upload-hint">Supports PDF files up to 10MB</span>
                    </div>

                    <div class="file-selected" *ngIf="selectedFile">
                      <i class="fa-solid fa-file-pdf"></i>
                      <div class="file-info">
                        <span class="file-name">{{selectedFile.name}}</span>
                        <span class="file-size">{{formatFileSize(selectedFile.size)}}</span>
                      </div>
                      <button class="remove-file-btn" (click)="removeFile($event)">
                        <i class="fa-solid fa-times"></i>
                      </button>
                    </div>
                  </div>

                  <!-- Proceed Button -->
                  <button class="option-btn proceed-btn"
                          *ngIf="selectedFile"
                          (click)="generateQuestionsFromPDF()"
                          [disabled]="isGeneratingFromPDF">
                    <i class="fa-solid" [ngClass]="isGeneratingFromPDF ? 'fa-spinner fa-spin' : 'fa-arrow-right'"></i>
                    {{isGeneratingFromPDF ? 'Processing PDF...' : 'Proceed with PDF'}}
                  </button>
                </div>

                <!-- OR Divider -->
                <div class="or-divider">
                  <span>or</span>
                </div>

                <!-- AI Generation Option -->
                <div class="generation-option">
                  <button class="option-btn ai-btn" (click)="generateQuestions()" [disabled]="isGenerating">
                    <i class="fa-solid fa-magic"></i>
                    Generate with AI
                  </button>
                </div>
              </div>
            </div>

            <!-- Empty State After Deletion -->
            <div class="empty-after-deletion" *ngIf="generatedQuestions.length === 0 && !isGenerating && hasEverHadQuestions()">
              <div class="empty-content">
                <div class="empty-icon">
                  <i class="fa-solid fa-comments"></i>
                </div>
                <h3>No Questions Available</h3>
                <p>Use the AI Assistant on the right to generate new questions.</p>
                <div class="arrow-pointer">
                  <i class="fa-solid fa-arrow-right"></i>
                  <span>Try the AI Assistant</span>
                </div>
              </div>
            </div>
            </div>
          </div>

          <!-- Right Side: Technology Section -->
          <div class="management-content-right">
            <div class="technology-section">
            

              <div class="technology-input-area">
                  <!-- AI Prompt Input -->
                <div class="input-container">
                  <div class="input-header">
                    <label class="input-label">
                      <i class="fa-solid fa-robot"></i>
                      AI Assistant
                    </label>
                  </div>
                  <textarea class="technology-textarea"
                            [(ngModel)]="aiPrompt"
                            [placeholder]="generatedQuestions.length === 0 ? 'Generate 5 JavaScript questions' : 'Add 2 hard React questions about hooks'"
                            rows="4"></textarea>
                  <div class="input-actions">
                    <button class="ai-submit-btn"
                            (click)="submitAIPrompt()"
                            [disabled]="!aiPrompt.trim() || isGeneratingFromPrompt"
                            type="button">
                      <i class="fa-solid" [ngClass]="isGeneratingFromPrompt ? 'fa-spinner fa-spin' : 'fa-paper-plane'"></i>
                      {{isGeneratingFromPrompt ? 'Generating...' : 'Generate'}}
                    </button>
                  </div>
                </div>

                <!-- Smart Suggestions (Below Textarea) -->
                <div class="smart-suggestions" *ngIf="smartSuggestions.length > 0">
                  <div class="suggestions-header">
                    <h4 class="suggestions-title">
                      <i class="fa-solid fa-lightbulb"></i>
                      Quick Suggestions
                    </h4>
                  </div>
                  <div class="suggestions-list">
                    <button class="suggestion-item"
                            *ngFor="let suggestion of smartSuggestions"
                            (click)="useSuggestion(suggestion)"
                            type="button">
                      <i class="fa-solid fa-magic"></i>
                      <span class="suggestion-text">{{suggestion}}</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Results Tab -->
      <div class="tab-pane" *ngIf="activeTab === 'results'">
        <div class="schedule-content-layout">
          <!-- Left Side: Participants Table -->
          <div class="schedule-content-left">
            <div class="participants-section">
              <div class="section-header">
                <h2 class="section-title">Participants ({{participants.length}})</h2>
                <div class="table-actions">
                  <button class="download-btn" (click)="downloadResults()" *ngIf="participants.length > 0">
                    <i class="fa-solid fa-download"></i> Download Excel
                  </button>
                </div>
              </div>
              <div class="participants-table">

      <!-- Table header for assessment type -->
      <div class="table-header" *ngIf="eventType === 'assessment'">
        <div class="header-cell name-cell sortable" [ngClass]="{'active-sort': sortColumn === 'name'}"
          (click)="sortBy('name')">
          <span class="header-content">
            Name
            <i class="fa-solid" [ngClass]="getSortIconClass('name')"></i>
          </span>
        </div>
        <div class="header-cell batch-cell sortable" [ngClass]="{'active-sort': sortColumn === 'batch'}"
          (click)="sortBy('batch')">
          <span class="header-content">
            Batch
            <i class="fa-solid" [ngClass]="getSortIconClass('batch')"></i>
          </span>
        </div>
        <div class="header-cell completed-cell sortable" [ngClass]="{'active-sort': sortColumn === 'completedAt'}"
          (click)="sortBy('completedAt')">
          <span class="header-content">
            Completed At
            <i class="fa-solid" [ngClass]="getSortIconClass('completedAt')"></i>
          </span>
        </div>
        <div class="header-cell time-cell sortable" [ngClass]="{'active-sort': sortColumn === 'timeTaken'}"
          (click)="sortBy('timeTaken')">
          <span class="header-content">
            Duration
            <i class="fa-solid" [ngClass]="getSortIconClass('timeTaken')"></i>
          </span>
        </div>
        <div class="header-cell score-cell sortable" [ngClass]="{'active-sort': sortColumn === 'score'}"
          (click)="sortBy('score')">
          <span class="header-content">
            Percentage
            <i class="fa-solid" [ngClass]="getSortIconClass('score')"></i>
          </span>
        </div>
        <div class="header-cell result-cell sortable" [ngClass]="{'active-sort': sortColumn === 'result'}"
          (click)="sortBy('result')">
          <span class="header-content">
            Result
            <i class="fa-solid" [ngClass]="getSortIconClass('result')"></i>
          </span>
        </div>
      </div>

      <!-- Table header for challenge type -->
      <div class="table-header" *ngIf="eventType === 'challenge'">
        <div class="header-cell name-cell sortable" [ngClass]="{'active-sort': sortColumn === 'name'}"
          (click)="sortBy('name')">
          <span>Name</span>
          <i class="fa-solid" [ngClass]="getSortIconClass('name')"></i>
        </div>
        <div class="header-cell batch-cell sortable" [ngClass]="{'active-sort': sortColumn === 'batch'}"
          (click)="sortBy('batch')">
          <span>Batch</span>
          <i class="fa-solid" [ngClass]="getSortIconClass('batch')"></i>
        </div>
        <div class="header-cell completed-cell sortable" [ngClass]="{'active-sort': sortColumn === 'completedAt'}"
          (click)="sortBy('completedAt')">
          <span>Completed At</span>
          <i class="fa-solid" [ngClass]="getSortIconClass('completedAt')"></i>
        </div>
        <div class="header-cell time-cell sortable" [ngClass]="{'active-sort': sortColumn === 'timeTaken'}"
          (click)="sortBy('timeTaken')">
          <span>Attempts</span>
          <i class="fa-solid" [ngClass]="getSortIconClass('timeTaken')"></i>
        </div>
        <div class="header-cell score-cell sortable" [ngClass]="{'active-sort': sortColumn === 'score'}"
          (click)="sortBy('score')">
          <span>XP Earned</span>
          <i class="fa-solid" [ngClass]="getSortIconClass('score')"></i>
        </div>
        <div class="header-cell result-cell sortable" [ngClass]="{'active-sort': sortColumn === 'result'}"
          (click)="sortBy('result')">
          <span>Status</span>
          <i class="fa-solid" [ngClass]="getSortIconClass('result')"></i>
        </div>
      </div>

      <!-- Table header for interview/default type -->
      <div class="table-header" *ngIf="eventType !== 'assessment' && eventType !== 'challenge'">
        <div class="header-cell name-cell sortable" [ngClass]="{'active-sort': sortColumn === 'name'}"
          (click)="sortBy('name')">
          <span>Name</span>
          <i class="fa-solid" [ngClass]="getSortIconClass('name')"></i>
        </div>
        <div class="header-cell batch-cell sortable" [ngClass]="{'active-sort': sortColumn === 'batch'}"
          (click)="sortBy('batch')">
          <span>Batch</span>
          <i class="fa-solid" [ngClass]="getSortIconClass('batch')"></i>
        </div>
        <div class="header-cell completed-cell sortable" [ngClass]="{'active-sort': sortColumn === 'completedAt'}"
          (click)="sortBy('completedAt')">
          <span>Completed At</span>
          <i class="fa-solid" [ngClass]="getSortIconClass('completedAt')"></i>
        </div>
        <div class="header-cell time-cell sortable" [ngClass]="{'active-sort': sortColumn === 'timeTaken'}"
          (click)="sortBy('timeTaken')">
          <span>Time Taken</span>
          <i class="fa-solid" [ngClass]="getSortIconClass('timeTaken')"></i>
        </div>
        <div class="header-cell score-cell sortable" [ngClass]="{'active-sort': sortColumn === 'score'}"
          (click)="sortBy('score')">
          <span>Avg Score</span>
          <i class="fa-solid" [ngClass]="getSortIconClass('score')"></i>
        </div>
        <div class="header-cell result-cell sortable" [ngClass]="{'active-sort': sortColumn === 'result'}"
          (click)="sortBy('result')">
          <span>Result</span>
          <i class="fa-solid" [ngClass]="getSortIconClass('result')"></i>
        </div>
      </div>

      <!-- Empty state when no participants -->
      <div class="empty-state" *ngIf="participants.length === 0">
        <div class="empty-icon">
          <i class="fa-solid fa-users-slash"></i>
        </div>
        <h3>No Results Found</h3>
        <p>There are no participants who have completed this schedule yet.</p>
        <p *ngIf="schedule?.assignedBatches?.length">This schedule is assigned to {{schedule?.assignedBatches?.length}}
          batch(es).</p>
      </div>

      <!-- Participants list -->
      <div class="table-body" *ngIf="participants.length > 0">
        <div class="table-row" *ngFor="let participant of participants" (click)="onParticipantClick(participant)">
          <div class="cell name-cell">
            <span class="participant-name">{{participant.studentName || participant.name || 'Unknown'}}</span>
            <span class="top-performer" *ngIf="getScore(participant) >= 80">🏆</span>
          </div>
          <div class="cell batch-cell">
            <span *ngIf="participant.batchId">{{participant.batchId}}</span>
            <span *ngIf="!participant.batchId">-</span>
          </div>
          <div class="cell completed-cell">
            <span *ngIf="getCompletionDate(participant)">{{getCompletionDate(participant) | date:'MMM d, y, h:mm
              a'}}</span>
            <span *ngIf="!getCompletionDate(participant)">-</span>
          </div>
          <div class="cell time-cell">
            {{getDuration(participant)}}
          </div>
          <div class="cell score-cell">
            <ng-container *ngIf="eventType === 'challenge'">{{participant.xpEarned || 0}}</ng-container>
            <ng-container *ngIf="eventType !== 'challenge'">{{getScore(participant)}}%</ng-container>
          </div>
          <div class="cell result-cell">
            <span class="result-badge" [ngClass]="getResultClass(participant)">
              {{getResultText(participant)}}
            </span>
          </div>
        </div>
      </div>
              </div>
            </div>
          </div>

          <!-- Right Side: Analytics Section -->
          <div class="schedule-content-right">
            <div class="analytics-section">
              <!-- Tabs -->
              <div class="tabs-container">
                <div class="tabs-header">
                  <div class="tab-wrapper">
                    <div class="tab active">
                      Analytics
                    </div>
                  </div>
                </div>
              </div>

              <!-- Analytics Cards -->
              <div class="analytics-cards">
                <!-- Completion Rate Card -->
                <div class="analytics-card">
                  <div class="card-header">
                    <h3>Completion Rate</h3>
                  </div>
                  <div class="card-content">
                    <div class="metric-value">{{getCompletionRate()}}%</div>
                    <div class="metric-details">
                      <span>Completed: {{participants.length}}</span>
                      <span>Total Assigned: {{getTotalAssigned()}}</span>
                    </div>
                  </div>
                </div>

                <!-- Average Score Card -->
                <div class="analytics-card">
                  <div class="card-header">
                    <h3>Average Score</h3>
                  </div>
                  <div class="card-content">
                    <div class="metric-value">{{getAverageScore() | number:'1.0-0'}}%</div>
                    <div class="metric-details">
                      <span>Highest: {{getHighestScore()}}%</span>
                      <span>Lowest: {{getLowestScore()}}%</span>
                    </div>
                  </div>
                </div>

                <!-- Performance Distribution Card -->
                <div class="analytics-card">
                  <div class="card-header">
                    <h3>Performance Distribution</h3>
                  </div>
                  <div class="card-content">
                    <div class="metric-value">{{getTopPerformersCount()}}</div>
                    <div class="metric-label">Top Performers (80%+)</div>
                    <div class="metric-details">
                      <span>Excellent: {{getPerformanceCount('excellent')}}</span>
                      <span>Good: {{getPerformanceCount('good')}}</span>
                    </div>
                  </div>
                </div>

                <!-- Time Analytics Card -->
                <div class="analytics-card">
                  <div class="card-header">
                    <h3>Time Analytics</h3>
                  </div>
                  <div class="card-content">
                    <div class="metric-value">{{getAverageTime()}}</div>
                    <div class="metric-label">Average Duration</div>
                    <div class="metric-details">
                      <span>Fastest: {{getFastestTime()}}</span>
                      <span>Slowest: {{getSlowestTime()}}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Settings Tab -->
      <div class="tab-pane" *ngIf="activeTab === 'settings'">
        <div class="settings-layout">
          <div class="settings-section">
            <div class="section-header">
              <h2 class="section-title">Schedule Settings</h2>
            </div>

            <div class="settings-content">
              <div class="settings-card">
                <h3>Basic Information</h3>
                <div class="settings-grid">
                  <div class="setting-item">
                    <label>Title</label>
                    <span>{{scheduleTitle}}</span>
                  </div>
                  <div class="setting-item">
                    <label>Type</label>
                    <span>{{scheduleType}}</span>
                  </div>
                  <div class="setting-item">
                    <label>Difficulty</label>
                    <span>{{scheduleDifficulty}}</span>
                  </div>
                  <div class="setting-item" *ngIf="schedule?.skillName">
                    <label>Skill</label>
                    <span>{{schedule?.skillName}}</span>
                  </div>
                  <div class="setting-item" *ngIf="schedule?.testTopic">
                    <label>Topics</label>
                    <span>{{schedule?.testTopic}}</span>
                  </div>
                  <div class="setting-item" *ngIf="schedule?.numberOfQuestions">
                    <label>Number of Questions</label>
                    <span>{{schedule?.numberOfQuestions}}</span>
                  </div>
                </div>
              </div>

              <div class="settings-card" *ngIf="schedule?.assignedBatches?.length">
                <h3>Assigned Batches</h3>
                <div class="batch-list">
                  <div class="batch-item" *ngFor="let batchId of schedule?.assignedBatches">
                    <i class="fa-solid fa-users"></i>
                    <span>{{batchId}}</span>
                  </div>
                </div>
              </div>

              <div class="settings-card">
                <h3>Schedule Dates</h3>
                <div class="settings-grid">
                  <div class="setting-item" *ngIf="schedule?.scheduleDate">
                    <label>Start Date</label>
                    <span>{{schedule?.scheduleDate | date:'MMM d, y, h:mm a'}}</span>
                  </div>
                  <div class="setting-item" *ngIf="schedule?.endDate">
                    <label>End Date</label>
                    <span>{{schedule?.endDate | date:'MMM d, y, h:mm a'}}</span>
                  </div>
                  <div class="setting-item" *ngIf="schedule?.testDuration">
                    <label>Duration</label>
                    <span>{{schedule?.testDuration}}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
