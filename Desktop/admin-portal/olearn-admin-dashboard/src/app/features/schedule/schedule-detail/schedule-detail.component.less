// Main container styles
.schedule-detail-container {
  padding: 0 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Main content layout
.schedule-content-layout {
  display: flex;
  gap: 20px;
  flex: 1;
  min-height: 0;
}

// Management layout
.management-layout {
  display: flex;
  gap: 20px;
  flex: 1;
  min-height: 0;
}

// Left side - Participants table
.schedule-content-left {
  flex: 2;
  min-width: 0;

  .participants-section {
    background-color: #1a1a1a;
    border-radius: 12px;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      .section-title {
        font-size: 18px;
        font-weight: 500;
        margin: 0;
        color: #fff;
      }
    }
  }
}

// Management content left side
.management-content-left {
  flex: 3;
  min-width: 0;

  .management-section {
    background-color: #1a1a1a;
    border-radius: 12px;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      .section-title {
        font-size: 18px;
        font-weight: 500;
        margin: 0;
        color: #fff;
      }
    }
  }
}

// Management content right side
.management-content-right {
  flex: 1;
  min-width: 280px;
  max-width: 320px;

  .technology-section {
    background-color: #1a1a1a;
    border-radius: 12px;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      .section-title {
        font-size: 18px;
        font-weight: 500;
        margin: 0;
        color: #fff;
      }
    }
  }
}

// Right side - Analytics section
.schedule-content-right {
  min-width: 300px;

  .analytics-section {
    background-color: #1a1a1a;
    border-radius: 12px;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

// Results summary styles
.results-summary {
  margin-bottom: 20px;

  .section-title {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 15px;
    color: #fff;
  }

  .summary-stats {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;

    .stat-item {
      background-color: #1a1a1a;
      border-radius: 8px;
      padding: 15px 20px;
      min-width: 120px;

      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #fff;
        margin-bottom: 5px;
      }

      .stat-label {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }
}

// Loading container styles
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  margin: 20px 0;
  background-color: #1a1a1a;
  border-radius: 12px;

  .spinner {
    margin-bottom: 15px;

    i {
      font-size: 32px;
      color: #3CAA56;
    }
  }

  p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    margin: 0;
  }
}

// Back button styles
.back-button-container {
  .back-btn {
    background-color: transparent;
    color: rgba(255, 255, 255, 0.7);
    border: none;
    padding: 8px 0;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;

    &:hover {
      color: white;
      transform: translateX(-3px);
    }

    i {
      font-size: 12px;
      transition: transform 0.3s ease;
    }

    &:hover i {
      transform: translateX(-3px);
    }
  }
}

// Schedule header styles
.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .schedule-info {
    flex: 1;
    padding: 0;

    .title-meta-row {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
      gap: 15px;
    }

    .schedule-title {
      margin: 0;
      color: #fff;
      font-size: 24px;
      font-weight: 500;
    }

    .schedule-meta {
      display: flex;
      align-items: center;
      gap: 15px;

      .participant-count {
        color: rgba(255, 255, 255, 0.7);
        font-size: 14px;
      }

      .schedule-tag {
        background-color: #3CAA56;
        color: white;
        padding: 1px 9px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }

      .difficulty-tag {
        background-color: #f6e05e;
        color: #1a202c;
        padding: 1px 9px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
    }

    // Schedule details styles
    .schedule-details {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-top: 15px;
      padding: 12px 15px;
      background-color: #202020;
      border-radius: 8px;

      .detail-item {
        display: flex;
        align-items: center;
        gap: 8px;
        color: rgba(255, 255, 255, 0.7);
        font-size: 13px;

        i {
          color: #3CAA56;
          font-size: 14px;
        }
      }
    }
  }
}

// Analytics tabs and cards styles
.tabs-container {
  .tabs-header {
    background-color: #202020;
    padding: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .tab-wrapper {
      .tab {
        padding: 15px 20px;
        color: rgba(255, 255, 255, 0.7);
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        transition: all 0.2s ease;
      }
    }
  }
}

.analytics-cards {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  flex: 1;
  overflow-y: auto;
}

.analytics-card {
  background-color: #202020;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);

  .card-header {
    margin-bottom: 12px;

    h3 {
      font-size: 14px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.7);
      margin: 0;
    }
  }

  .card-content {
    .metric-value {
      font-size: 24px;
      font-weight: 600;
      color: #fff;
      margin-bottom: 8px;
    }

    .metric-label {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.6);
      margin-bottom: 8px;
    }

    .metric-details {
      display: flex;
      flex-direction: column;
      gap: 4px;

      span {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}

// Participants table styles
.participants-table {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

// Table actions styles
.table-actions {
  display: flex;
  justify-content: flex-end;
  padding: 15px 20px 0;

  .download-btn {
    background: linear-gradient(to right, #3CAA56, #7A86F2);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;

    &:hover {
      opacity: 0.9;
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }

    i {
      font-size: 14px;
    }
  }
}

// Table header styles
.table-header {
  display: flex;
  background-color: #202020;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .header-cell {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 500;
    text-align: center; // Center align all header cells

    &.sortable {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center; // Center the content
      transition: color 0.2s ease;

      &:hover {
        color: #fff;
      }

      .header-content {
        // Keep text and icon together
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px; // Add some space between text and icon
      }

      i {
        margin-left: 5px;
        font-size: 16px;
        opacity: 0.7;
        transition: all 0.2s ease;
        color: #7A86F2; // Use the gradient color for better visibility
      }

      &:hover i {
        opacity: 1;
        transform: translateY(-1px);
      }

      &.active-sort {
        color: #fff;
        font-weight: 600;

        i {
          opacity: 1;
          color: #3CAA56;
          font-size: 18px; // Make the active sort icon larger
          text-shadow: 0 0 5px rgba(60, 170, 86, 0.5); // Add a subtle glow effect
        }
      }
    }
  }
}

// Table body styles
.table-body {
  flex: 1;
  overflow-y: auto;
}

// Table row styles
.table-row {
  display: flex;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: rgba(255, 255, 255, 0.03);
  }

  .cell {
    color: #fff;
    font-size: 14px;
    text-align: center; // Center align all cells
  }

  .name-cell {
    display: flex;
    align-items: center;
    justify-content: center; // Center the content horizontally
    gap: 8px;

    .participant-name {
      cursor: pointer;
      transition: color 0.2s ease;

      &:hover {
        color: #f6e05e;
        text-decoration: underline;
      }
    }

    .top-performer {
      font-size: 16px;
    }
  }
}

// Cell widths
.name-cell {
  flex: 2;
}

.batch-cell {
  flex: 1;
}

.completed-cell {
  flex: 2;
  text-align: center;
}

.time-cell {
  flex: 1;
  text-align: center;
}

.score-cell {
  flex: 1;
  text-align: center;
}

.result-cell {
  flex: 1.5;
  text-align: center;

  .result-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }
}

// Empty state styles
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;

  .empty-icon {
    font-size: 40px;
    color: rgba(255, 255, 255, 0.2);
    margin-bottom: 15px;
  }

  h3 {
    font-size: 18px;
    font-weight: 500;
    color: #fff;
    margin-bottom: 10px;
  }

  p {
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    max-width: 400px;
  }
}

.result-excellent {
  background-color: #48BB78;
  color: white;
}

.result-good {
  background-color: #4299E1;
  color: white;
}

.result-average {
  background-color: #ECC94B;
  color: #1A202C;
}

.result-fair {
  background-color: #ED8936;
  color: white;
}

.result-needs-improvement {
  background-color: #F56565;
  color: white;
}

// Responsive styles
@media (max-width: 1200px) {
  .schedule-content-right {
    min-width: 250px;
  }

  .analytics-card {
    .card-content {
      .metric-value {
        font-size: 20px;
      }
    }
  }
}

@media (max-width: 992px) {
  .schedule-content-layout {
    flex-direction: column;
    gap: 15px;
  }

  .schedule-content-left,
  .schedule-content-right {
    flex: none;
    min-width: auto;
  }

  .schedule-content-right {
    .analytics-cards {
      flex-direction: row;
      overflow-x: auto;
      gap: 10px;

      .analytics-card {
        min-width: 200px;
        flex-shrink: 0;
      }
    }
  }

  .table-header,
  .table-row {
    padding: 12px 15px;
  }

  .header-cell,
  .cell {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .schedule-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    padding-bottom: 15px;

    .schedule-info {
      width: 100%;

      .title-meta-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }

      .schedule-title {
        font-size: 20px;
      }

      .schedule-meta {
        flex-wrap: wrap;
        gap: 10px;
      }

      .schedule-details {
        padding: 10px;
        gap: 10px;

        .detail-item {
          font-size: 12px;
        }
      }
    }
  }

  .schedule-content-right {
    .analytics-cards {
      padding: 15px;

      .analytics-card {
        min-width: 150px;
        padding: 12px;

        .card-content {
          .metric-value {
            font-size: 18px;
          }
        }
      }
    }
  }

  .table-header,
  .table-row {
    padding: 10px;
  }

  .header-cell,
  .cell {
    font-size: 12px;
  }

  .completed-cell,
  .time-cell {
    display: none;
  }

  .result-cell {
    flex: 1;
  }

  .name-cell {
    flex: 3;
  }

  .type-cell {
    flex: 2;
  }
}

  .score-cell {
    flex: 1;
  }


@media (max-width: 576px) {
  .schedule-detail-container {
    padding: 15px;
  }

  .schedule-header {
    padding-bottom: 10px;

    .schedule-info {
      .title-meta-row {
        gap: 8px;
      }

      .schedule-title {
        font-size: 18px;
      }

      .schedule-meta {
        .participant-count {
          font-size: 12px;
        }

        .schedule-tag,
        .difficulty-tag {
          font-size: 10px;
          padding: 3px 8px;
        }
      }

      .schedule-details {
        padding: 8px;
        gap: 8px;

        .detail-item {
          font-size: 11px;

          i {
            font-size: 12px;
          }
        }
      }
    }
  }

  .table-header,
  .table-row {
    padding: 8px;
  }

  .header-cell,
  .cell {
    font-size: 11px;
  }

  .type-cell {
    display: none;
  }

  .name-cell {
    flex: 3;
  }

  .score-cell {
    flex: 1;
  }
}

// Schedule tabs styles
.schedule-tabs {
  display: flex;
  background-color: #202020;
  border-radius: 8px 8px 0 0;
  margin-bottom: 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .tab {
    padding: 15px 20px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;

    &.active {
      color: #fff;
      border-bottom-color: #7A86F2;
      background-color: rgba(122, 134, 242, 0.1);
    }

    &:hover:not(.active) {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.05);
    }

    i {
      font-size: 16px;
    }
  }
}

// Tab content styles
.tab-content {
  background-color: #1a1a1a;
  border-radius: 0 0 12px 12px;
  min-height: 600px;
  overflow: hidden;
  display: block;
}

.tab-pane {
  height: 100%;
  min-height: 500px;
  display: block;
}

// Management layout styles
.management-layout {
  height: calc(100vh - 200px); // Fixed height to prevent page scroll
  min-height: 500px;
  display: flex;
}

.management-section {
  background-color: #202020;
  border-radius: 8px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; // Important for flex child to shrink
  overflow: hidden;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .section-title {
      font-size: 16px;
      font-weight: 500;
      margin: 0;
      color: #fff;
    }

    .management-actions {
      display: flex;
      gap: 10px;

      .action-btn {
        padding: 8px 12px;
        border: none;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 6px;

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        &.generate-btn {
          background-color: #48BB78;
          color: white;

          &:hover:not(:disabled) {
            background-color: #38a169;
            transform: translateY(-1px);
          }
        }

        &.save-draft-btn {
          background-color: #6c757d;
          color: white;

          &:hover:not(:disabled) {
            background-color: #5a6268;
            transform: translateY(-1px);
          }
        }

        &.publish-btn {
          background-color: #48BB78;
          color: white;

          &:hover:not(:disabled) {
            background-color: #38a169;
            transform: translateY(-1px);
          }
        }

        &.save-btn {
          background-color: #48BB78;
          color: white;

          &:hover:not(:disabled) {
            background-color: #38a169;
          }
        }
      }
    }
  }
}

// Missing questions indicator styles
.missing-questions-indicator {
  margin: 10px;
  padding: 12px 16px;
  background-color: rgba(72, 187, 120, 0.1);
  border: 1px solid rgba(72, 187, 120, 0.3);
  border-radius: 8px;
  margin-bottom: 15px;

  .indicator-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;

    .indicator-text {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #48BB78;
      font-size: 14px;

      i {
        font-size: 16px;
      }
    }

    .generate-missing-btn {
      padding: 8px 12px;
      border: none;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 6px;
      background-color: #48BB78;
      color: white;

      &:hover:not(:disabled) {
        background-color: #38a169;
        transform: translateY(-1px);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}

// Content list styles
.content-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  min-height: 0; // Important for flex child to shrink
  max-height: calc(100vh - 400px); // Ensure it doesn't exceed viewport
}

.content-item {
  margin-bottom: 8px;
  background-color: #1a1a1a;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
  overflow: hidden;

  &:hover {
    border-color: rgba(255, 255, 255, 0.2);
  }

  .content-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    position: relative;

    &:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }

    .content-details {
      flex: 1;
      min-width: 0;

      .content-text {
        font-size: 14px;
        color: #fff;
        margin-bottom: 6px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .content-meta {
        display: flex;
        gap: 8px;
        align-items: center;

        .difficulty,
        .type {
          font-size: 11px;
          padding: 2px 6px;
          border-radius: 4px;
          background-color: rgba(72, 187, 120, 0.2);
          color: #48BB78;
        }

        .expand-indicator {
          color: rgba(255, 255, 255, 0.4);
          font-size: 12px;
          margin-left: auto;
          transition: color 0.2s ease;
          position: absolute;
          top: 16px;
          right: 52px;

          &:hover {
            color: #48BB78;
          }
        }
      }
    }

    .content-actions {
      display: flex;
      gap: 4px;

      .action-btn-small {
        padding: 6px 8px;
        border: none;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.7);
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba(255, 255, 255, 0.2);
          color: #fff;
        }

        &.delete-btn:hover {
          background-color: #F56565;
          color: white;
        }
      }
    }
  }

  // Question preview styles
  .question-preview {
    padding: 0 15px 20px 15px;
    background-color: rgba(255, 255, 255, 0.02);
    border-top: 1px solid rgba(255, 255, 255, 0.05);

    h4 {
      color: #48BB78;
      font-size: 13px;
      font-weight: 600;
      margin: 15px 0 10px 0;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    // Code block styles
    .code-block {
      margin: 15px 0;
      border-radius: 6px;
      overflow: hidden;
      background-color: rgba(0, 0, 0, 0.3);

      .code-header {
        background-color: rgba(72, 187, 120, 0.1);
        padding: 8px 12px;
        border-bottom: 1px solid rgba(72, 187, 120, 0.2);

        .code-language {
          color: #48BB78;
          font-size: 12px;
          font-weight: 500;
          text-transform: uppercase;
        }
      }

      pre {
        margin: 0;
        padding: 15px;
        background: transparent;
        color: #e2e8f0;
        font-family: 'Courier New', monospace;
        font-size: 13px;
        line-height: 1.4;
        overflow-x: auto;

        code {
          background: transparent;
          color: inherit;
          padding: 0;
        }
      }
    }

    // Options styles
    .options-section {
      .options-list {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .option-item {
          display: flex;
          align-items: center;
          gap: 10px;
          padding: 10px 12px;
          border-radius: 6px;
          background-color: rgba(255, 255, 255, 0.03);
          border: 1px solid rgba(255, 255, 255, 0.1);
          transition: all 0.2s ease;

          &.correct-option {
            background-color: rgba(72, 187, 120, 0.1);
            border-color: rgba(72, 187, 120, 0.3);
          }

          .option-label {
            color: #48BB78;
            font-weight: 600;
            font-size: 13px;
            min-width: 20px;
          }

          .option-text {
            color: #fff;
            font-size: 13px;
            flex: 1;
          }

          .correct-indicator {
            color: #48BB78;
            font-size: 14px;
          }
        }
      }
    }

    // Explanation styles
    .explanation-section {
      .explanation-text {
        color: rgba(255, 255, 255, 0.8);
        font-size: 13px;
        line-height: 1.5;
        margin: 0;
        padding: 12px;
        background-color: rgba(255, 255, 255, 0.03);
        border-radius: 6px;
        border-left: 3px solid #48BB78;
      }
    }

    // Topics styles
    .topics-section {
      .topics-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;

        .topic-tag {
          background-color: rgba(72, 187, 120, 0.2);
          color: #48BB78;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 11px;
          font-weight: 500;
        }
      }
    }
  }
}

// Loading state for content generation
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  flex: 1;
  min-height: 300px;
  max-height: calc(100vh - 400px); // Constrain height
  overflow: hidden;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }

  .loading-icon {
    position: relative;

    .ai-brain {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: linear-gradient(135deg, rgba(72, 187, 120, 0.2), rgba(72, 187, 120, 0.1));
      border: 2px solid rgba(72, 187, 120, 0.3);

      .brain-pulse {
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        border: 2px solid #48BB78;
        animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
      }

      i {
        font-size: 32px;
        color: #48BB78;
        z-index: 2;
        animation: brain-glow 2s ease-in-out infinite alternate;
      }
    }
  }

  .loading-text {
    display: flex;
    align-items: center;
    gap: 2px;
    font-size: 18px;
    font-weight: 500;
    color: #48BB78;
    min-height: 25px;

    .typing-text {
      display: inline-block;
    }

    .cursor {
      display: inline-block;
      animation: blink 1s infinite;
      color: #48BB78;
      font-weight: 300;
    }
  }
}

// Animations
@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes brain-glow {
  0% {
    text-shadow: 0 0 5px rgba(72, 187, 120, 0.5);
  }
  100% {
    text-shadow: 0 0 20px rgba(72, 187, 120, 0.8), 0 0 30px rgba(72, 187, 120, 0.6);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

// Content generation options
.content-generation-options {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  flex: 1;
  max-height: calc(100vh - 400px);
  overflow-y: auto;

  .generation-methods {
    max-width: 600px;
    width: 100%;

    h3 {
      font-size: 20px;
      font-weight: 600;
      color: #fff;
      margin-bottom: 8px;
    }

    > p {
      color: rgba(255, 255, 255, 0.7);
      font-size: 14px;
      margin-bottom: 30px;
    }

    .generation-option {
      background-color: #1a1a1a;
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 24px;
      margin-bottom: 20px;
      transition: all 0.2s ease;

      &:hover {
        border-color: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
      }

      .option-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;

        i {
          font-size: 24px;
          color: #48BB78;
        }

        h4 {
          font-size: 18px;
          font-weight: 600;
          color: #fff;
          margin: 0;
        }
      }

      > p {
        color: rgba(255, 255, 255, 0.6);
        font-size: 14px;
        margin-bottom: 20px;
        text-align: left;
      }

      .option-btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s ease;
        margin: 0 auto;

        &.ai-btn {
          background-color: #48BB78;
          color: white;

          &:hover:not(:disabled) {
            background-color: #38a169;
            transform: translateY(-1px);
          }

          &:disabled {
            background-color: rgba(72, 187, 120, 0.5);
            cursor: not-allowed;
          }
        }

        &.proceed-btn {
          background-color: #3CAA56;
          color: white;
          margin-top: 16px;

          &:hover:not(:disabled) {
            background-color: #2d8a43;
            transform: translateY(-1px);
          }

          &:disabled {
            background-color: rgba(60, 170, 86, 0.5);
            cursor: not-allowed;
          }
        }
      }

      // PDF Upload Area
      .pdf-upload-area {
        border: 2px dashed rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        padding: 32px 20px;
        margin: 16px 0;
        cursor: pointer;
        transition: all 0.2s ease;
        background-color: rgba(255, 255, 255, 0.02);

        &:hover {
          border-color: rgba(255, 255, 255, 0.5);
          background-color: rgba(255, 255, 255, 0.05);
        }

        &.dragover {
          border-color: #48BB78;
          background-color: rgba(72, 187, 120, 0.1);
        }

        .upload-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;

          i {
            font-size: 48px;
            color: rgba(255, 255, 255, 0.4);
          }

          .upload-text {
            font-size: 16px;
            font-weight: 500;
            color: #fff;
            margin: 0;
          }

          .upload-hint {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
          }
        }

        .file-selected {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px;
          background-color: rgba(72, 187, 120, 0.1);
          border-radius: 6px;
          border: 1px solid rgba(72, 187, 120, 0.3);

          i {
            font-size: 24px;
            color: #48BB78;
          }

          .file-info {
            flex: 1;
            text-align: left;

            .file-name {
              display: block;
              font-size: 14px;
              font-weight: 500;
              color: #fff;
              margin-bottom: 4px;
            }

            .file-size {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.6);
            }
          }

          .remove-file-btn {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;

            &:hover {
              color: #F56565;
              background-color: rgba(245, 101, 101, 0.1);
            }

            i {
              font-size: 16px;
            }
          }
        }
      }
    }
  }
}

// Empty state for content (fallback)
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  flex: 1;
  max-height: calc(100vh - 400px); // Constrain height
  overflow: hidden;

  .empty-icon {
    font-size: 48px;
    color: rgba(255, 255, 255, 0.2);
    margin-bottom: 15px;
  }

  h3 {
    font-size: 18px;
    font-weight: 500;
    color: #fff;
    margin-bottom: 10px;
  }

  p {
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    margin-bottom: 20px;
    max-width: 300px;
  }

  .generate-btn-large {
    padding: 12px 24px;
    background-color: #48BB78;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;

    &:hover {
      background-color: #38a169;
      transform: translateY(-1px);
    }
  }
}

// Question preview styles
.question-preview {
  padding: 20px;
  flex: 1;
  overflow-y: auto;

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .question-number {
      font-size: 16px;
      font-weight: 600;
      color: #7A86F2;
    }

    .preview-actions {
      .action-btn {
        padding: 8px 12px;
        border: none;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 6px;

        &.edit-btn {
          background-color: #f6e05e;
          color: #1a202c;

          &:hover {
            background-color: #ecc94b;
          }
        }
      }
    }
  }

  .preview-content {
    .question-text {
      font-size: 16px;
      color: #fff;
      margin-bottom: 20px;
      line-height: 1.5;
    }

    .options-list {
      margin-bottom: 20px;

      .option-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 10px 12px;
        margin-bottom: 8px;
        background-color: #1a1a1a;
        border-radius: 6px;
        border: 1px solid rgba(255, 255, 255, 0.1);

        &.correct {
          border-color: #3CAA56;
          background-color: rgba(60, 170, 86, 0.1);
        }

        .option-label {
          font-weight: 600;
          color: #7A86F2;
          min-width: 20px;
        }

        .option-text {
          flex: 1;
          color: #fff;
        }

        .correct-indicator {
          color: #3CAA56;
          font-size: 16px;
        }
      }
    }

    .question-details {
      .detail-item {
        display: flex;
        margin-bottom: 8px;

        .label {
          font-weight: 500;
          color: rgba(255, 255, 255, 0.7);
          min-width: 80px;
        }

        .value {
          color: #fff;
        }
      }
    }
  }
}

// Question editor styles
.question-editor {
  padding: 20px;
  flex: 1;
  overflow-y: auto;

  .editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .question-number {
      font-size: 16px;
      font-weight: 600;
      color: #7A86F2;
    }

    .editor-actions {
      display: flex;
      gap: 8px;

      .action-btn {
        padding: 8px 12px;
        border: none;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 6px;

        &.save-btn {
          background-color: #48BB78;
          color: white;

          &:hover {
            background-color: #38a169;
          }
        }

        &.cancel-btn {
          background-color: #F56565;
          color: white;

          &:hover {
            background-color: #e53e3e;
          }
        }
      }
    }
  }

  .editor-content {
    .form-group {
      margin-bottom: 20px;

      label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 8px;
      }

      .form-control {
        width: 100%;
        padding: 10px 12px;
        background-color: #1a1a1a;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 6px;
        color: #fff;
        font-size: 14px;

        &:focus {
          outline: none;
          border-color: #7A86F2;
          box-shadow: 0 0 0 2px rgba(122, 134, 242, 0.2);
        }

        &::placeholder {
          color: rgba(255, 255, 255, 0.4);
        }
      }

      .question-textarea {
        resize: vertical;
        min-height: 80px;
      }

      .explanation-textarea {
        resize: vertical;
        min-height: 60px;
      }
    }

    .options-editor {
      .option-editor {
        margin-bottom: 12px;

        .option-input-group {
          display: flex;
          align-items: center;
          gap: 10px;

          .option-label {
            font-weight: 600;
            color: #7A86F2;
            min-width: 20px;
          }

          .option-input {
            flex: 1;
          }

          .option-controls {
            .correct-checkbox {
              display: flex;
              align-items: center;
              gap: 6px;
              color: rgba(255, 255, 255, 0.7);
              cursor: pointer;

              input[type="radio"] {
                accent-color: #3CAA56;
              }

              span {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}

// No selection state
.no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  flex: 1;

  .no-selection-icon {
    font-size: 48px;
    color: rgba(255, 255, 255, 0.2);
    margin-bottom: 15px;
  }

  h3 {
    font-size: 18px;
    font-weight: 500;
    color: #fff;
    margin-bottom: 10px;
  }

  p {
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    max-width: 300px;
  }
}

// Settings layout styles
.settings-layout {
  .settings-section {
    background-color: #202020;
    border-radius: 8px;

    .section-header {
      padding: 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      .section-title {
        font-size: 18px;
        font-weight: 500;
        margin: 0;
        color: #fff;
      }
    }

    .settings-content {
      padding: 20px;

      .settings-card {
        background-color: #1a1a1a;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        border: 1px solid rgba(255, 255, 255, 0.1);

        &:last-child {
          margin-bottom: 0;
        }

        h3 {
          font-size: 16px;
          font-weight: 500;
          color: #fff;
          margin-bottom: 15px;
        }

        .settings-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 15px;

          .setting-item {
            display: flex;
            flex-direction: column;
            gap: 5px;

            label {
              font-size: 12px;
              font-weight: 500;
              color: rgba(255, 255, 255, 0.6);
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }

            span {
              font-size: 14px;
              color: #fff;
              padding: 8px 0;
            }
          }
        }

        .batch-list {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;

          .batch-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background-color: #202020;
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.1);

            i {
              color: #3CAA56;
              font-size: 14px;
            }

            span {
              color: #fff;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

// Topic grouping styles
.topic-group {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }

  .topic-header {
    margin-bottom: 12px;

    .topic-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #3CAA56;
      padding: 8px 0;

      i {
        font-size: 14px;
        color: #3CAA56;
      }

      .topic-name {
        color: #fff;
      }

      .topic-count {
        color: rgba(255, 255, 255, 0.6);
        font-weight: 400;
        font-size: 14px;
      }
    }
  }

  .topic-questions {
    padding-left: 16px;
    border-left: 2px solid rgba(60, 170, 86, 0.2);

    .content-item {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // Multi-topic group specific styling
  &.multi-topic-group {
    .topic-header {
      .topic-title {
        i {
          color: #FFA500; // Orange color for multi-topic groups
        }

        .topic-name {
          color: #FFA500;
          font-weight: 600;
        }

        .topic-count {
          color: rgba(255, 165, 0, 0.8);
          font-weight: 500;
        }
      }
    }

    .topic-questions {
      border-left-color: rgba(255, 165, 0, 0.3);
    }
  }
}

// Technology input area styles
.technology-input-area {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;

  // Smart suggestions styles
  .smart-suggestions {
    margin-top: 16px;

    .suggestions-header {
      margin-bottom: 12px;

      .suggestions-title {
        font-size: 13px;
        font-weight: 600;
        color: #FFA500;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 6px;

        i {
          color: #FFA500;
          font-size: 12px;
        }
      }
    }

    .suggestions-list {
      display: flex;
      flex-direction: column;
      gap: 6px;

      .suggestion-item {
        background-color: rgba(255, 165, 0, 0.08);
        border: 1px solid rgba(255, 165, 0, 0.2);
        color: #FFA500;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        text-align: left;
        width: 100%;

        &:hover {
          background-color: rgba(255, 165, 0, 0.15);
          border-color: rgba(255, 165, 0, 0.4);
          transform: translateX(2px);
        }

        &:active {
          transform: translateX(0);
        }

        i {
          font-size: 10px;
          flex-shrink: 0;
        }

        .suggestion-text {
          flex: 1;
          line-height: 1.3;
        }
      }
    }
  }

  .input-container {
    .input-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .input-label {
        font-size: 14px;
        font-weight: 600;
        color: #3CAA56;
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: #3CAA56;
        }
      }

      .input-hint {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);
      }
    }

    .technology-textarea {
      width: 100%;
      min-height: 80px;
      background-color: #202020;
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 15px;
      color: #fff;
      font-size: 14px;
      font-family: inherit;
      resize: vertical;
      transition: all 0.3s ease;

      &::placeholder {
        color: rgba(255, 255, 255, 0.4);
      }

      &:focus {
        outline: none;
        border-color: #3CAA56;
        box-shadow: 0 0 0 2px rgba(60, 170, 86, 0.1);
      }
    }

    .input-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 12px;

      .ai-submit-btn {
        background-color: #3CAA56;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 6px;

        &:hover:not(:disabled) {
          background-color: #2d8a43;
          transform: translateY(-1px);
        }

        &:active:not(:disabled) {
          transform: translateY(0);
        }

        &:disabled {
          background-color: rgba(60, 170, 86, 0.5);
          cursor: not-allowed;
          transform: none;
        }

        i {
          font-size: 10px;
        }
      }
    }
  }

  .technology-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .tag-item {
      display: flex;
      align-items: center;
      background-color: #3CAA56;
      color: white;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 500;
      gap: 8px;
      transition: all 0.2s ease;

      &:hover {
        background-color: #2d8a43;
        transform: translateY(-1px);
      }

      .tag-text {
        line-height: 1;
      }

      .tag-remove {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 0;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        font-size: 10px;

        &:hover {
          background-color: rgba(255, 255, 255, 0.2);
        }

        i {
          font-size: 10px;
        }
      }
    }
  }


}