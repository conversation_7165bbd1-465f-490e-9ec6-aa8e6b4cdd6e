import { <PERSON>mponent, <PERSON><PERSON>nit, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
  FormsModule,
} from '@angular/forms';
import { NgbDropdownModule, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { HeaderService } from '../../../core/services/header.service';
import { Router } from '@angular/router';
import { Schedule } from '../models/schedule.model';
import { ScheduleService } from '../services/schedule.service';
import { ScheduleDataService } from '../services/schedule-data.service';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { BatchService } from '../../batches/services/batch.service';
import { Batch } from '../../batches/models/batch.model';
import { AdminActivityService } from '../../../core/services/admin-activity.service';
import { ConfirmationDialogComponent } from '../../../shared/components/confirmation-dialog/confirmation-dialog.component';
import { AuthService } from '../../../core/services/auth.service';
import { ToastService } from '../../../core/services/toast.service';
import { RoleService } from '../../../core/services/role.service';

@Component({
  selector: 'app-schedule',
  standalone: true,
  imports: [CommonModule, NgbDropdownModule, ReactiveFormsModule, FormsModule],
  templateUrl: './schedule.component.html',
  styleUrls: [
    './schedule.component.less',
    './schedule-modal.less',
    './schedule-modal-global.less',
    './schedule-tabs-override.css',
    './update-buttons-override.css',
    './form-inputs-override.css',
    './question-buttons-override.css',
    './date-time-inputs.css',
  ],
})
export class ScheduleComponent implements OnInit, OnDestroy {
  private subscriptions: Subscription[] = [];
  activeTab: 'upcoming' | 'completed' = 'upcoming';
  selectedFilter: string = 'all'; // Default filter is 'all'
  selectedBatchFilter: string = ''; // Default batch filter is empty (all batches)
  selectedSortOption: string = 'dateAdded'; // Default sort option is 'dateAdded'
  searchQuery: string = ''; // Search query for filtering schedules
  allSchedules: any[] = []; // Store all schedules for filtering
  isSearching: boolean = false; // Flag to track if we're currently searching
  scheduleTypes = [
    { id: 'assessment', name: 'Skill Assessment' },
    { id: 'challenge', name: 'Code Challenge' },
    { id: 'interview', name: 'Mock Interview' },
  ];

  // Filter options for the dropdown
  filterOptions = [
    { id: 'all', name: 'All Types' },
    { id: 'assessment', name: 'Skill Assessments' },
    { id: 'challenge', name: 'Code Challenges' },
    { id: 'interview', name: 'Mock Interviews' },
  ];

  // Sort options for the dropdown
  sortOptions = [
    { id: 'dateAdded', name: 'Date Added' },
    { id: 'lastModified', name: 'Last Modified' },
  ];

  selectedScheduleType: string = '';
  assessmentForm: FormGroup;
  interviewForm: FormGroup;
  challengeForm: FormGroup;
  difficultyLevels = ['Easy', 'Medium', 'Hard'];
  questionOptions = [10, 20, 30];
  interviewQuestionOptions = [3, 4, 5];
  selectedBatches: string[] = []; // Changed to string array for Firebase
  selectedFile: File | null = null;
  isLoading = false;
  batches: Batch[] = [];
  isEditMode = false; // Flag to track if we're editing an existing schedule
  sendCalendarInvites = true; // Flag to control whether to send calendar invites

  @ViewChild('interviewModal') interviewModal: any;

  schedules: Schedule[] = [];
  hasSchedules = false;
  editForm: FormGroup;
  currentSchedule: Schedule | null = null;
  showEditModal = false;

  // Map to store batch names by ID for quick lookup
  batchNameMap: Map<string, string> = new Map();

  constructor(
    private headerService: HeaderService,
    private fb: FormBuilder,
    private modalService: NgbModal,
    private router: Router,
    private scheduleService: ScheduleService,
    private batchService: BatchService,
    private scheduleDataService: ScheduleDataService,
    private adminActivityService: AdminActivityService,
    private authService: AuthService,
    private toastService: ToastService,
    private roleService: RoleService
  ) {
    this.editForm = this.fb.group({
      title: ['', Validators.required],
      type: ['', Validators.required],
      category: ['', Validators.required],
      scheduleDate: ['', Validators.required],
      time: ['', Validators.required],
      difficulty: ['Easy', Validators.required],
    });

    this.assessmentForm = this.fb.group({
      title: ['', Validators.required], // Custom title field
      skillName: ['', Validators.required],
      testTopic: ['', Validators.required],
      scheduleDate: ['', Validators.required],
      scheduleEndDate: ['', Validators.required], // New end date field
      scheduleTime: ['', Validators.required], // Renamed to startTime in UI
      scheduleEndTime: ['', Validators.required], // New end time field
      testDuration: ['', Validators.required],
      difficultyLevel: ['', Validators.required],
      numberOfQuestions: ['', Validators.required],
    });

    this.interviewForm = this.fb.group({
      title: ['', Validators.required], // Custom title field
      interviewType: ['', Validators.required],
      scheduleDate: ['', Validators.required],
      scheduleEndDate: ['', Validators.required], // New end date field
      scheduleTime: ['', Validators.required], // Renamed to startTime in UI
      scheduleEndTime: ['', Validators.required], // New end time field
      skillName: [''],
      interviewTopics: [''], // New field for topics
      difficultyLevel: ['', Validators.required],
      numberOfQuestions: ['', Validators.required],
      testDuration: ['', Validators.required],
    });

    this.challengeForm = this.fb.group({
      title: ['', Validators.required], // Custom title field
      skillName: ['', Validators.required],
      challengeTopic: ['', Validators.required],
      scheduleDate: ['', Validators.required],
      scheduleEndDate: ['', Validators.required], // New end date field
      scheduleTime: ['', Validators.required], // Renamed to startTime in UI
      scheduleEndTime: ['', Validators.required], // New end time field
      difficultyLevel: ['', Validators.required],
    });
  }

  editSchedule(scheduleId: string, event: Event, content: any): void {
    event.stopPropagation();
    const scheduleToEdit = this.schedules.find(
      (s) => s.scheduleId === scheduleId
    );

    if (scheduleToEdit) {
      this.currentSchedule = scheduleToEdit;
      this.isEditMode = true;

      // Set the selected schedule type based on the schedule being edited
      this.selectedScheduleType = scheduleToEdit.type;

      // Populate the appropriate form based on the schedule type
      if (scheduleToEdit.type === 'assessment') {
        this.assessmentForm.patchValue({
          title:
            scheduleToEdit.title ||
            `Skill Assessment: ${scheduleToEdit.skillName || ''}`,
          skillName: scheduleToEdit.skillName || '',
          testTopic: scheduleToEdit.testTopic || '',
          scheduleDate: new Date(scheduleToEdit.scheduleDate)
            .toISOString()
            .split('T')[0],
          scheduleEndDate: scheduleToEdit['endDate']
            ? new Date(scheduleToEdit['endDate']).toISOString().split('T')[0]
            : new Date(scheduleToEdit.scheduleDate).toISOString().split('T')[0],
          scheduleTime: scheduleToEdit.startTime || scheduleToEdit.time || '',
          scheduleEndTime: scheduleToEdit.endTime || '',
          testDuration: scheduleToEdit.testDuration || '',
          difficultyLevel: scheduleToEdit.difficulty,
          numberOfQuestions: scheduleToEdit.numberOfQuestions || '',
        });
      } else if (scheduleToEdit.type === 'interview') {
        this.interviewForm.patchValue({
          title:
            scheduleToEdit.title ||
            `${scheduleToEdit.interviewType || 'Mock'} Interview`,
          interviewType: scheduleToEdit.interviewType || '',
          scheduleDate: new Date(scheduleToEdit.scheduleDate)
            .toISOString()
            .split('T')[0],
          scheduleEndDate: scheduleToEdit['endDate']
            ? new Date(scheduleToEdit['endDate']).toISOString().split('T')[0]
            : new Date(scheduleToEdit.scheduleDate).toISOString().split('T')[0],
          scheduleTime: scheduleToEdit.startTime || scheduleToEdit.time || '',
          scheduleEndTime: scheduleToEdit.endTime || '',
          skillName: scheduleToEdit.skillName || '',
          interviewTopics: scheduleToEdit.testTopic || '',
          difficultyLevel: scheduleToEdit.difficulty,
          numberOfQuestions: scheduleToEdit.numberOfQuestions || '',
          testDuration: scheduleToEdit.testDuration || '',
        });
      } else if (scheduleToEdit.type === 'challenge') {
        this.challengeForm.patchValue({
          title:
            scheduleToEdit.title ||
            `Code Challenge: ${scheduleToEdit.skillName || ''}`,
          skillName: scheduleToEdit.skillName || '',
          challengeTopic:
            scheduleToEdit.challengeTopic || scheduleToEdit.category || '',
          scheduleDate: new Date(scheduleToEdit.scheduleDate)
            .toISOString()
            .split('T')[0],
          scheduleEndDate: scheduleToEdit['endDate']
            ? new Date(scheduleToEdit['endDate']).toISOString().split('T')[0]
            : new Date(scheduleToEdit.scheduleDate).toISOString().split('T')[0],
          scheduleTime: scheduleToEdit.startTime || scheduleToEdit.time || '',
          scheduleEndTime: scheduleToEdit.endTime || '',
          difficultyLevel: scheduleToEdit.difficulty,
        });
      }

      // Set selected batches
      this.selectedBatches = scheduleToEdit.assignedBatches || [];

      // Open the schedule modal
      const modalRef = this.modalService.open(content, {
        centered: true,
        backdrop: 'static',
        keyboard: true, // Allow ESC key to close the modal
        size: 'lg',
        windowClass: 'schedule-modal',
        modalDialogClass: 'schedule-modal-dialog',
        backdropClass: 'schedule-modal-backdrop',
      });

      // Add event listener for modal close/dismiss
      modalRef.result.then(
        (result) => {
          console.log('Edit modal closed with result:', result);
        },
        (reason) => {
          console.log('Edit modal dismissed with reason:', reason);
        }
      );

      // No need to apply inline styles as we're using CSS classes
    }
  }

  /**
   * Update a schedule using the current form values
   * @param modal The modal to close after successful update
   */
  updateScheduleFromForm(modal: any): void {
    if (!this.currentSchedule) {
      console.error('No schedule selected for update');
      return;
    }

    let updateData: Partial<Schedule> = {};

    // Get form values based on schedule type
    if (
      this.selectedScheduleType === 'assessment' &&
      this.assessmentForm.valid
    ) {
      const formValues = this.assessmentForm.value;
      updateData = {
        title: formValues.title || 'Skill Assessment: ' + formValues.skillName,
        scheduleDate: new Date(formValues.scheduleDate).getTime(),
        endDate: new Date(formValues.scheduleEndDate).getTime(),
        time: formValues.scheduleTime,
        startTime: formValues.scheduleTime,
        endTime: formValues.scheduleEndTime,
        difficulty: formValues.difficultyLevel,
        skillName: formValues.skillName,
        testTopic: formValues.testTopic,
        testDuration: formValues.testDuration,
        numberOfQuestions: formValues.numberOfQuestions,
        assignedBatches: [...this.selectedBatches],
      };
    } else if (
      this.selectedScheduleType === 'interview' &&
      this.interviewForm.valid
    ) {
      const formValues = this.interviewForm.value;
      updateData = {
        title: formValues.title || `${formValues.interviewType} Interview`,
        scheduleDate: new Date(formValues.scheduleDate).getTime(),
        endDate: new Date(formValues.scheduleEndDate).getTime(),
        time: formValues.scheduleTime,
        startTime: formValues.scheduleTime,
        endTime: formValues.scheduleEndTime,
        difficulty: formValues.difficultyLevel,
        interviewType: formValues.interviewType,
        numberOfQuestions: formValues.numberOfQuestions,
        // Always include skill name and topics
        skillName: formValues.skillName || '',
        testTopic: formValues.interviewTopics || '',
        assignedBatches: [...this.selectedBatches],
        // Include testDuration for interviews
        testDuration: formValues.testDuration,
        // PDF upload removed as requested
        fileAttached: false,
      };
    } else if (
      this.selectedScheduleType === 'challenge' &&
      this.challengeForm.valid
    ) {
      const formValues = this.challengeForm.value;
      updateData = {
        title: formValues.title || 'Code Challenge: ' + formValues.skillName,
        category: formValues.challengeTopic,
        scheduleDate: new Date(formValues.scheduleDate).getTime(),
        endDate: new Date(formValues.scheduleEndDate).getTime(),
        time: formValues.scheduleTime,
        startTime: formValues.scheduleTime,
        endTime: formValues.scheduleEndTime,
        difficulty: formValues.difficultyLevel,
        skillName: formValues.skillName,
        challengeTopic: formValues.challengeTopic,
        assignedBatches: [...this.selectedBatches],
      };
    } else {
      console.error('Invalid form or schedule type');
      return;
    }

    // Show loading indicator
    this.isLoading = true;

    // Update in Firebase
    const subscription = this.scheduleService
      .updateSchedule(this.currentSchedule.scheduleId, updateData)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: (success) => {
          if (success) {
            console.log('Schedule updated successfully');
            // Reload schedules to get the updated list
            this.loadSchedules();
            modal.close();
          } else {
            console.error('Failed to update schedule');
          }
        },
        error: (error) => {
          console.error('Error updating schedule:', error);
        },
      });

    this.subscriptions.push(subscription);
    this.currentSchedule = null;
    this.isEditMode = false;
  }

  // Keep the old saveSchedule method for backward compatibility
  saveSchedule(modal: any): void {
    if (this.editForm.valid && this.currentSchedule) {
      const formValues = this.editForm.value;

      // Create update object
      const updateData: Partial<Schedule> = {
        title: formValues.title,
        type: formValues.type,
        category: formValues.category,
        scheduleDate: new Date(formValues.scheduleDate).getTime(),
        time: formValues.time,
        difficulty: formValues.difficulty,
      };

      // Show loading indicator
      this.isLoading = true;

      // Update in Firebase
      const subscription = this.scheduleService
        .updateSchedule(this.currentSchedule.scheduleId, updateData)
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe({
          next: (success) => {
            if (success) {
              console.log('Schedule updated successfully');
              // Reload schedules to get the updated list
              this.loadSchedules();
              modal.close();
            } else {
              console.error('Failed to update schedule');
            }
          },
          error: (error) => {
            console.error('Error updating schedule:', error);
          },
        });

      this.subscriptions.push(subscription);

      this.currentSchedule = null;
    }
  }

  deleteSchedule(scheduleId: string, event: Event): void {
    event.stopPropagation();

    // Check if user has admin/superadmin role using RoleService
    this.roleService.isSuperAdmin().subscribe(isAdmin => {
      if (!isAdmin) {
        this.toastService.error('You do not have permission to delete schedules.');
        return;
      }

      this.proceedWithScheduleDeletion(scheduleId);
    });
  }

  private proceedWithScheduleDeletion(scheduleId: string): void {
    this.currentSchedule =
      this.schedules.find((s) => s.scheduleId === scheduleId) || null;

    if (this.currentSchedule) {
      // Get the schedule title for display
      const scheduleTitle =
        this.currentSchedule.title || `Schedule ${scheduleId}`;

      // Open the confirmation dialog using the shared component
      const modalRef = this.modalService.open(ConfirmationDialogComponent, {
        centered: true,
        backdrop: 'static',
        keyboard: false,
        windowClass: 'delete-confirmation-modal',
      });

      // Set the dialog properties
      modalRef.componentInstance.title = 'Delete Schedule';
      modalRef.componentInstance.message = `
        <p>Are you sure you want to delete <strong>"${scheduleTitle}"</strong>?</p>
        <p class="mt-2 text-warning"><i class="fa-solid fa-triangle-exclamation me-2"></i>This action cannot be undone and will remove this schedule from all assigned batches.</p>
      `;
      modalRef.componentInstance.confirmButtonText = 'Delete Schedule';
      modalRef.componentInstance.cancelButtonText = 'Cancel';
      modalRef.componentInstance.confirmButtonClass = 'btn-danger';
      modalRef.componentInstance.icon = 'fa-trash';

      // Handle the result
      modalRef.result.then(
        (result) => {
          if (result) {
            console.log(`Deleting schedule ${scheduleId} (${scheduleTitle})`);

            // Show loading indicator
            this.isLoading = true;

            // Delete from Firebase
            const subscription = this.scheduleService
              .deleteSchedule(scheduleId)
              .pipe(finalize(() => (this.isLoading = false)))
              .subscribe({
                next: (success) => {
                  if (success) {
                    console.log('Schedule deleted successfully');

                    // Log the schedule deletion activity
                    this.adminActivityService
                      .logActivity('schedule', 'deleted', {
                        scheduleId: scheduleId,
                        scheduleTitle: scheduleTitle,
                        scheduleType: this.currentSchedule?.type || 'unknown',
                        skillName: this.currentSchedule?.skillName || '',
                        assignedBatches:
                          this.currentSchedule?.assignedBatches || [],
                        deletedAt: new Date().getTime(),
                      })
                      .subscribe({
                        next: () =>
                          console.log(
                            `Logged deletion of schedule ${scheduleId}`
                          ),
                        error: (logErr) =>
                          console.error(
                            `Error logging schedule deletion: ${logErr}`
                          ),
                      });

                    // Reload schedules to get the updated list
                    this.loadSchedules();
                  } else {
                    console.error('Failed to delete schedule');
                  }
                },
                error: (error) => {
                  console.error('Error deleting schedule:', error);
                },
              });

            this.subscriptions.push(subscription);
          }
          this.currentSchedule = null;
        },
        () => {
          // Modal dismissed
          this.currentSchedule = null;
        }
      );
    }
  }

  ngOnInit(): void {
    this.headerService.setPageTitle('Schedule Management');
    this.loadSchedules();
    this.loadBatches();

    // Initialize admin data cache for role checking
    this.roleService.initializeAdminData().subscribe({
      next: (isAdmin) => {
        console.log('Admin role initialized:', isAdmin);
      },
      error: (error) => {
        console.error('Error initializing admin data:', error);
      }
    });
  }

  /**
   * Load batches from Firebase (only active batches for scheduling)
   */
  loadBatches(): void {
    this.isLoading = true;
    const subscription = this.batchService
      .getAllBatches()
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: (batches) => {
          // Filter to show only active batches for scheduling
          const activeBatches = batches.filter(
            (batch) => this.determineBatchStatus(batch) === 'active'
          );
          this.batches = activeBatches;
          console.log('Loaded active batches for scheduling:', activeBatches);

          // Create a map of batch IDs to batch names for quick lookup
          activeBatches.forEach((batch) => {
            // Store both with and without 'batch' prefix for flexibility
            const batchId = batch.batchId;
            const fullBatchId = batchId.startsWith('batch')
              ? batchId
              : `batch${batchId}`;

            this.batchNameMap.set(batchId, batch.name);
            this.batchNameMap.set(fullBatchId, batch.name);
          });
        },
        error: (error) => {
          console.error('Error loading batches:', error);
        },
      });

    this.subscriptions.push(subscription);
  }

  /**
   * Set the filter type
   * @param filterType The type of schedule to filter by
   */
  setFilterType(filterType: string): void {
    this.selectedFilter = filterType;
    this.loadSchedules();
  }

  /**
   * Set the batch filter
   * @param batchId The batch ID to filter by, or empty string for all batches
   */
  setBatchFilter(batchId: string): void {
    this.selectedBatchFilter = batchId;
    this.loadSchedules();
  }

  /**
   * Set the sort option
   * @param sortOption The sort option to use
   */
  setSortOption(sortOption: string): void {
    this.selectedSortOption = sortOption;
    this.loadSchedules();
  }

  /**
   * Get the name of the current sort option
   * @returns The name of the current sort option
   */
  getSortOptionName(): string {
    const option = this.sortOptions.find(
      (opt) => opt.id === this.selectedSortOption
    );
    return option ? option.name : 'Date Added';
  }

  /**
   * Handle search input changes
   */
  onSearchChange(): void {
    this.isSearching = this.searchQuery.trim() !== '';
    this.loadSchedules();
  }

  /**
   * Clear the search input and reset the search state
   */
  clearSearch(): void {
    this.searchQuery = '';
    this.isSearching = false;
    this.loadSchedules();
  }

  /**
   * Load schedules from Firebase
   */
  loadSchedules(): void {
    this.isLoading = true;
    const subscription = this.scheduleService
      .getAllSchedules()
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: (schedules) => {
          // Store all schedules for filtering
          this.allSchedules = [...schedules];

          // Apply filters
          let filteredSchedules = [...schedules];

          // Apply type filter if not 'all'
          if (this.selectedFilter !== 'all') {
            filteredSchedules = filteredSchedules.filter(
              (schedule) => schedule.type === this.selectedFilter
            );
          }

          // Apply batch filter if a batch is selected
          if (this.selectedBatchFilter) {
            filteredSchedules = filteredSchedules.filter((schedule) => {
              // Check if the schedule has assigned batches
              if (
                schedule.assignedBatches &&
                schedule.assignedBatches.length > 0
              ) {
                // Check if the selected batch is in the assigned batches
                return schedule.assignedBatches.some((batchId) => {
                  // Handle both with and without 'batch' prefix
                  const normalizedBatchId = batchId.toString();
                  const normalizedSelectedBatchId =
                    this.selectedBatchFilter.toString();

                  return (
                    normalizedBatchId === normalizedSelectedBatchId ||
                    normalizedBatchId === `batch${normalizedSelectedBatchId}` ||
                    `batch${normalizedBatchId}` === normalizedSelectedBatchId
                  );
                });
              }
              return false;
            });
          }

          // Apply search filter if query exists
          if (this.searchQuery && this.searchQuery.trim() !== '') {
            const query = this.searchQuery.toLowerCase().trim();
            filteredSchedules = filteredSchedules.filter((schedule) => {
              // Search in title
              if (
                schedule.title &&
                schedule.title.toLowerCase().includes(query)
              ) {
                return true;
              }

              // Search in skill name
              if (
                schedule.skillName &&
                schedule.skillName.toLowerCase().includes(query)
              ) {
                return true;
              }

              // Search in topics
              if (
                schedule.testTopic &&
                schedule.testTopic.toLowerCase().includes(query)
              ) {
                return true;
              }

              // Search in batch names
              if (
                schedule.assignedBatches &&
                schedule.assignedBatches.length > 0
              ) {
                for (const batchId of schedule.assignedBatches) {
                  const batchName = this.getBatchName(batchId);
                  if (batchName && batchName.toLowerCase().includes(query)) {
                    return true;
                  }
                }
              }

              return false;
            });
          }

          // Continue with the rest of the filtering
          schedules = filteredSchedules;

          console.log('Loaded schedules:', schedules);

          // Apply tab filter (upcoming or completed)
          const now = new Date().getTime(); // Current timestamp

          // First, mark schedules as completed based on end date and end time
          schedules.forEach((schedule) => {
            try {
              // Get current date and time components
              const currentDate = new Date();
              const currentYear = currentDate.getFullYear();
              const currentMonth = currentDate.getMonth();
              const currentDay = currentDate.getDate();
              const currentHours = currentDate.getHours();
              const currentMinutes = currentDate.getMinutes();

              // Default to not completed
              schedule.isCompleted = false;

              // Check if the schedule has an end date
              if (schedule.endDate) {
                const endDate = new Date(schedule.endDate);

                // If end date is in the past (earlier day), it's completed
                if (
                  endDate < currentDate &&
                  (endDate.getFullYear() < currentYear ||
                    endDate.getMonth() < currentMonth ||
                    endDate.getDate() < currentDay)
                ) {
                  schedule.isCompleted = true;
                }
                // If end date is today, check the end time
                else if (
                  endDate.getFullYear() === currentYear &&
                  endDate.getMonth() === currentMonth &&
                  endDate.getDate() === currentDay &&
                  schedule.endTime
                ) {
                  try {
                    // Parse end time (format: "HH:MM")
                    const timeParts = schedule.endTime.split(':');
                    if (timeParts.length >= 2) {
                      const endHours = parseInt(timeParts[0], 10);
                      const endMinutes = parseInt(timeParts[1], 10);

                      // Compare with current time
                      if (
                        endHours < currentHours ||
                        (endHours === currentHours &&
                          endMinutes <= currentMinutes)
                      ) {
                        schedule.isCompleted = true;
                      }
                    }
                  } catch (timeError) {
                    console.error('Error parsing end time:', timeError);
                  }
                }
              }
              // If only start date is available, use that with similar logic
              else if (schedule.scheduleDate) {
                const startDate = new Date(schedule.scheduleDate);

                // If start date is in the past and we have no end date info, assume it's completed
                if (
                  startDate < currentDate &&
                  (startDate.getFullYear() < currentYear ||
                    startDate.getMonth() < currentMonth ||
                    startDate.getDate() < currentDay)
                ) {
                  schedule.isCompleted = true;
                }
                // If start date is today and we have a time, check that
                else if (
                  startDate.getFullYear() === currentYear &&
                  startDate.getMonth() === currentMonth &&
                  startDate.getDate() === currentDay &&
                  (schedule.time || schedule.startTime)
                ) {
                  const timeStr = schedule.time || schedule.startTime;
                  if (timeStr) {
                    try {
                      const timeParts = timeStr.split(':');
                      if (timeParts.length >= 2) {
                        const startHours = parseInt(timeParts[0], 10);
                        const startMinutes = parseInt(timeParts[1], 10);

                        // For start time only (no end time), consider it completed if it's at least 1 hour past
                        if (
                          startHours + 1 < currentHours ||
                          (startHours + 1 === currentHours &&
                            startMinutes <= currentMinutes)
                        ) {
                          schedule.isCompleted = true;
                        }
                      }
                    } catch (timeError) {
                      console.error('Error parsing start time:', timeError);
                    }
                  }
                }
              }

              // Log completion status for debugging
              console.log(
                `Schedule ${schedule.title} (${schedule.scheduleId}): endDate=${
                  schedule.endDate
                    ? new Date(schedule.endDate).toLocaleDateString()
                    : 'N/A'
                }, endTime=${schedule.endTime || 'N/A'}, isCompleted=${
                  schedule.isCompleted
                }`
              );
            } catch (error) {
              console.error(
                'Error determining completion status:',
                error,
                schedule
              );
              schedule.isCompleted = false;
            }
          });

          // Then filter based on the active tab
          if (this.activeTab === 'upcoming') {
            schedules = schedules.filter((schedule) => !schedule.isCompleted);
          } else if (this.activeTab === 'completed') {
            schedules = schedules.filter((schedule) => schedule.isCompleted);
          }

          // Apply sorting based on the selected sort option
          if (this.selectedSortOption === 'dateAdded') {
            // Sort by createdAt or scheduleDate (newest first)
            schedules.sort((a, b) => {
              // Use createdAt if available, otherwise fall back to scheduleDate
              const dateA = a.createdAt || a.scheduleDate || 0;
              const dateB = b.createdAt || b.scheduleDate || 0;
              return dateB - dateA; // Descending order (newest first)
            });
          } else if (this.selectedSortOption === 'lastModified') {
            // Sort by updatedAt, createdAt, or scheduleDate (newest first)
            schedules.sort((a, b) => {
              // Use updatedAt if available, otherwise fall back to createdAt or scheduleDate
              const dateA = a.updatedAt || a.createdAt || a.scheduleDate || 0;
              const dateB = b.updatedAt || b.createdAt || b.scheduleDate || 0;
              return dateB - dateA; // Descending order (newest first)
            });
          }

          this.schedules = schedules;
          this.hasSchedules = schedules.length > 0;
        },
        error: (error) => {
          console.error('Error loading schedules:', error);
          this.hasSchedules = false;
        },
      });

    this.subscriptions.push(subscription);
  }

  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions to prevent memory leaks
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }

  /**
   * Check if the current user has the admin/superadmin role (synchronous for UI binding)
   * @returns True if the user is an admin or superadmin, false otherwise
   */
  isSuperAdmin(): boolean {
    return this.roleService.isSuperAdminSync();
  }

  /**
   * Set the active tab
   * @param tab The tab to set as active
   */
  setActiveTab(tab: 'upcoming' | 'completed'): void {
    this.activeTab = tab;
    this.loadSchedules(); // Reload schedules with the new filter
  }

  /**
   * Determine batch status based on start and end dates
   * @param batch The batch object
   * @returns Status string: 'upcoming', 'active', or 'completed'
   */
  determineBatchStatus(batch: any): 'upcoming' | 'active' | 'completed' {
    try {
      // If no start or end date, default to active
      if (!batch.startDate || !batch.endDate) {
        return 'active';
      }

      // Convert dates to Date objects
      const startDate = this.getDateObject(batch.startDate);
      const endDate = this.getDateObject(batch.endDate);
      const currentDate = new Date();

      // If dates are invalid, default to active
      if (
        !startDate ||
        !endDate ||
        isNaN(startDate.getTime()) ||
        isNaN(endDate.getTime())
      ) {
        return 'active';
      }

      // Normalize dates to compare only the date part (ignore time)
      const currentDateOnly = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());
      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());

      // If current date is before start date, batch is upcoming
      if (currentDateOnly < startDateOnly) {
        return 'upcoming';
      }

      // If current date is after end date, batch is completed
      if (currentDateOnly > endDateOnly) {
        return 'completed';
      }

      // If current date is between start and end dates (inclusive), batch is active
      return 'active';
    } catch (error) {
      console.error('Error determining batch status:', error);
      return 'active'; // Default to active in case of error
    }
  }

  /**
   * Convert any date format to a Date object
   */
  getDateObject(date: any): Date | null {
    if (!date) return null;

    // If it's already a Date object, return it
    if (date instanceof Date) {
      return date;
    }

    // If it's a timestamp (number), convert it
    if (typeof date === 'number') {
      return new Date(date);
    }

    // If it's a string, try to parse it
    if (typeof date === 'string') {
      const parsed = new Date(date);
      return isNaN(parsed.getTime()) ? null : parsed;
    }

    // If it's a Firestore Timestamp object
    if (date && typeof date.toDate === 'function') {
      return date.toDate();
    }

    return null;
  }

  /**
   * Get the batch name for a given batch ID
   * @param batchId The batch ID to get the name for
   * @returns The batch name or a default string if not found
   */
  getBatchName(batchId: string): string {
    // Try to get the batch name from the map
    const batchName = this.batchNameMap.get(batchId);

    if (batchName) {
      return batchName;
    }

    // If not found in the map, try to find the batch in the batches array
    const batch = this.batches.find(
      (b) => b.batchId === batchId || `batch${b.batchId}` === batchId
    );

    if (batch) {
      // Store in map for future lookups
      this.batchNameMap.set(batchId, batch.name);
      return batch.name;
    }

    // If still not found, return a formatted version of the batch ID
    return batchId.replace('batch', 'Batch ');
  }

  /**
   * Open the schedule modal
   * @param type The type of schedule to create
   * @param modal The modal template reference
   */
  openScheduleModal(type: string, modal: any): void {
    this.selectedScheduleType = type;
    this.isEditMode = false; // Reset edit mode flag
    this.currentSchedule = null; // Reset current schedule

    // Reset assessment form
    this.assessmentForm.reset();

    // Reset interview form
    this.interviewForm.reset();

    // Reset challenge form
    this.challengeForm.reset();

    // Reset selected batches
    this.selectedBatches = [];

    // Reset file selection
    this.selectedFile = null;

    // Reset calendar invites flag
    this.sendCalendarInvites = true;

    // Open the modal
    const modalRef = this.modalService.open(modal, {
      centered: true,
      backdrop: 'static',
      keyboard: true, // Allow ESC key to close the modal
      size: 'lg',
      windowClass: 'schedule-modal',
      modalDialogClass: 'schedule-modal-dialog',
      backdropClass: 'schedule-modal-backdrop',
    });

    // Add event listener for modal close/dismiss
    modalRef.result.then(
      (result) => {
        console.log('Modal closed with result:', result);
      },
      (reason) => {
        console.log('Modal dismissed with reason:', reason);
      }
    );
  }

  /**
   * Change the schedule type tab
   * @param type The schedule type to change to
   */
  changeScheduleType(type: string): void {
    // If in edit mode, don't allow changing the schedule type
    if (this.isEditMode) {
      console.log('Cannot change schedule type in edit mode');
      return;
    }

    this.selectedScheduleType = type;
    console.log(`Changed schedule type to: ${type}`);
  }

  /**
   * Close the modal
   * @param modal The modal reference to close
   */
  closeModal(modal: any): void {
    console.log('Closing modal...');
    try {
      modal.close('Cancel clicked');
    } catch (error) {
      console.error('Error closing modal:', error);
      try {
        modal.dismiss('Cancel clicked');
      } catch (dismissError) {
        console.error('Error dismissing modal:', dismissError);
      }
    }
  }

  scheduleInterview(modal: any): void {
    if (this.interviewForm.valid && !this.isLoading) {
      const formValues = this.interviewForm.value;

      // Create a new schedule from the form values
      const newSchedule: Schedule = {
        scheduleId: '', // Will be generated by the service
        type: 'interview',
        title: formValues.title || `${formValues.interviewType} Interview`,
        scheduleDate: new Date(formValues.scheduleDate).getTime(),
        endDate: new Date(formValues.scheduleEndDate).getTime(), // New field for end date
        time: formValues.scheduleTime, // Keep for backward compatibility
        startTime: formValues.scheduleTime, // New field for start time
        endTime: formValues.scheduleEndTime, // New field for end time
        difficulty: formValues.difficultyLevel,
        // Add interview-specific fields
        interviewType: formValues.interviewType,
        numberOfQuestions: formValues.numberOfQuestions,
        // Always include skill name and topics
        skillName: formValues.skillName || '',
        testTopic: formValues.interviewTopics || '',
        // PDF upload removed as requested
        fileAttached: false,
        // Add batch assignments
        assignedBatches: [...this.selectedBatches],
        // Add interview duration (similar to testDuration for assessments)
        testDuration: formValues.testDuration,
      };

      // Show loading indicator
      this.isLoading = true;

      // Save to Firebase
      const subscription = this.scheduleService
        .createSchedule(newSchedule, this.sendCalendarInvites)
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe({
          next: (scheduleId) => {
            if (scheduleId) {
              console.log(
                'Interview created successfully with ID:',
                scheduleId
              );

              // Close modal first
              modal.close();

              // Store the created schedule data for navigation
              const createdSchedule = { ...newSchedule, scheduleId };
              this.scheduleDataService.setCurrentSchedule(createdSchedule);

              // Navigate to schedule details page
              this.router.navigate(['/schedule', scheduleId]);
            } else {
              console.error('Failed to create interview');
              this.toastService.error('Failed to create interview. Please try again.');
            }
          },
          error: (error) => {
            console.error('Error creating interview:', error);
            this.toastService.error('Error creating interview. Please try again.');
          },
        });

      this.subscriptions.push(subscription);
    }
  }

  createAssessment(modal: any): void {
    if (this.assessmentForm.valid && !this.isLoading) {
      const formValues = this.assessmentForm.value;

      // Create a new schedule from the form values
      const newSchedule: Schedule = {
        scheduleId: '', // Will be generated by the service
        type: 'assessment',
        title: formValues.title || 'Skill Assessment: ' + formValues.skillName,
        scheduleDate: new Date(formValues.scheduleDate).getTime(),
        endDate: new Date(formValues.scheduleEndDate).getTime(), // New field for end date
        time: formValues.scheduleTime, // Keep for backward compatibility
        startTime: formValues.scheduleTime, // New field for start time
        endTime: formValues.scheduleEndTime, // New field for end time
        difficulty: formValues.difficultyLevel,
        // Add assessment-specific fields
        skillName: formValues.skillName,
        testTopic: formValues.testTopic,
        testDuration: formValues.testDuration,
        numberOfQuestions: formValues.numberOfQuestions,
        // Add batch assignments
        assignedBatches: [...this.selectedBatches],
      };

      // Show loading indicator
      this.isLoading = true;

      // Save to Firebase
      const subscription = this.scheduleService
        .createSchedule(newSchedule, this.sendCalendarInvites)
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe({
          next: (scheduleId) => {
            if (scheduleId) {
              console.log(
                'Assessment created successfully with ID:',
                scheduleId
              );

              // Close modal first
              modal.close();

              // Store the created schedule data for navigation
              const createdSchedule = { ...newSchedule, scheduleId };
              this.scheduleDataService.setCurrentSchedule(createdSchedule);

              // Navigate to schedule details page
              this.router.navigate(['/schedule', scheduleId]);
            } else {
              console.error('Failed to create assessment');
              this.toastService.error('Failed to create assessment. Please try again.');
            }
          },
          error: (error) => {
            console.error('Error creating assessment:', error);
            this.toastService.error('Error creating assessment. Please try again.');
          },
        });

      this.subscriptions.push(subscription);
    }
  }

  scheduleChallenge(modal: any): void {
    if (this.challengeForm.valid && !this.isLoading) {
      const formValues = this.challengeForm.value;

      // Create a new schedule from the form values
      const newSchedule: Schedule = {
        scheduleId: '', // Will be generated by the service
        type: 'challenge',
        title: formValues.title || 'Code Challenge: ' + formValues.skillName,
        category: formValues.challengeTopic,
        scheduleDate: new Date(formValues.scheduleDate).getTime(),
        endDate: new Date(formValues.scheduleEndDate).getTime(), // New field for end date
        time: formValues.scheduleTime, // Keep for backward compatibility
        startTime: formValues.scheduleTime, // New field for start time
        endTime: formValues.scheduleEndTime, // New field for end time
        difficulty: formValues.difficultyLevel,
        // Add challenge-specific fields
        skillName: formValues.skillName,
        challengeTopic: formValues.challengeTopic,
        // Add batch assignments
        assignedBatches: [...this.selectedBatches],
      };

      // Show loading indicator
      this.isLoading = true;

      // Save to Firebase
      const subscription = this.scheduleService
        .createSchedule(newSchedule, this.sendCalendarInvites)
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe({
          next: (scheduleId) => {
            if (scheduleId) {
              console.log(
                'Challenge created successfully with ID:',
                scheduleId
              );

              // Close modal first
              modal.close();

              // Store the created schedule data for navigation
              const createdSchedule = { ...newSchedule, scheduleId };
              this.scheduleDataService.setCurrentSchedule(createdSchedule);

              // Navigate to schedule details page
              this.router.navigate(['/schedule', scheduleId]);
            } else {
              console.error('Failed to create challenge');
              this.toastService.error('Failed to create challenge. Please try again.');
            }
          },
          error: (error) => {
            console.error('Error creating challenge:', error);
            this.toastService.error('Error creating challenge. Please try again.');
          },
        });

      this.subscriptions.push(subscription);
    }
  }

  // We don't need this method anymore as Firebase generates IDs
  // Keeping it for backward compatibility with any existing code
  generateUniqueId(): string {
    // Generate a timestamp-based ID
    return new Date().getTime().toString();
  }

  setDifficultyLevel(level: string): void {
    if (this.selectedScheduleType === 'assessment') {
      this.assessmentForm.patchValue({
        difficultyLevel: level,
      });
    } else if (this.selectedScheduleType === 'interview') {
      this.interviewForm.patchValue({
        difficultyLevel: level,
      });
    } else if (this.selectedScheduleType === 'challenge') {
      this.challengeForm.patchValue({
        difficultyLevel: level,
      });
    }
  }

  setNumberOfQuestions(count: number): void {
    if (this.selectedScheduleType === 'assessment') {
      this.assessmentForm.patchValue({
        numberOfQuestions: count,
      });
    } else if (this.selectedScheduleType === 'interview') {
      // Ensure the count is within the valid range for interviews
      if (this.interviewQuestionOptions.includes(count)) {
        this.interviewForm.patchValue({
          numberOfQuestions: count,
        });
      }
    }
    // Removed challenge option as it no longer has numberOfQuestions
  }

  // Methods moved to the bottom of the file

  isBatchSelected(batchNumber: number): boolean {
    // Convert number to string for comparison
    return this.selectedBatches.includes(batchNumber.toString());
  }

  /**
   * Toggle batch selection for assessment
   * @param batchNumber The batch number to toggle
   * @param event The change event from checkbox
   */
  toggleBatchSelection(batchNumber: number, event: Event): void {
    // Convert number to string for Firebase
    const batchId = batchNumber.toString();
    const index = this.selectedBatches.indexOf(batchId);
    const checkbox = event.target as HTMLInputElement;

    if (checkbox && checkbox.checked) {
      // Add to selected batches if not already there
      if (index === -1) {
        this.selectedBatches.push(batchId);
      }
    } else {
      // Remove from selected batches
      if (index !== -1) {
        this.selectedBatches.splice(index, 1);
      }
    }
  }

  // Method moved to the bottom of the file

  toggleBatch(batchNumber: number): void {
    const batchId = batchNumber.toString();
    if (this.isBatchSelected(batchNumber)) {
      // Don't allow deselecting if it's the only selected batch
      if (this.selectedBatches.length > 1) {
        this.selectedBatches = this.selectedBatches.filter(
          (b) => b !== batchId
        );
      }
    } else {
      this.selectedBatches.push(batchId);
    }
  }

  // Method moved to the bottom of the file

  /**
   * Remove the selected file
   * @param event The click event
   */
  removeFile(event: Event): void {
    event.stopPropagation(); // Prevent triggering the upload area click
    this.selectedFile = null;
  }

  /**
   * Handle file selection
   * @param event The change event from the file input
   */
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];
    }
  }

  /**
   * Handle file drop
   * @param event The drop event
   */
  onFileDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();

    if (event.dataTransfer && event.dataTransfer.files.length > 0) {
      const file = event.dataTransfer.files[0];

      // Check if the file is a PDF
      if (file.type === 'application/pdf') {
        this.selectedFile = file;
      } else {
        console.error('Only PDF files are allowed');
        // Could add a toast notification here
      }
    }
  }

  /**
   * Set the test duration
   * @param duration The duration to set
   */
  setTestDuration(duration: string): void {
    this.assessmentForm.patchValue({
      testDuration: duration,
    });
  }

  /**
   * Set the schedule time
   * @param time The time to set in HH:MM format
   */
  setScheduleTime(time: string): void {
    if (this.selectedScheduleType === 'assessment') {
      this.assessmentForm.patchValue({
        scheduleTime: time,
      });
    } else if (this.selectedScheduleType === 'interview') {
      this.interviewForm.patchValue({
        scheduleTime: time,
      });
    } else if (this.selectedScheduleType === 'challenge') {
      this.challengeForm.patchValue({
        scheduleTime: time,
      });
    }
  }

  /**
   * Set the interview type
   * @param type The interview type to set
   */
  setInterviewType(type: string): void {
    this.interviewForm.patchValue({
      interviewType: type,
    });

    // No longer resetting skill name and topics for HR interviews
  }

  /**
   * Check if the interview type requires a skill name
   * @returns True if the interview type requires a skill name
   */
  requiresSkillName(): boolean {
    const interviewType = this.interviewForm.get('interviewType')?.value;
    return (
      interviewType === 'technicalBased' ||
      interviewType === 'scenarioBased' ||
      interviewType === 'problemSolving'
    );
  }

  /**
   * Set the interview round
   * @param round The round to set
   */
  setRound(round: string): void {
    this.interviewForm.patchValue({
      round: round,
    });
  }

  /**
   * Navigate to schedule details page
   * @param scheduleId The ID of the schedule to view
   */
  viewScheduleDetails(scheduleId: string): void {
    // Find the schedule data from our loaded schedules
    const scheduleData = this.schedules.find(
      (schedule) => schedule.scheduleId === scheduleId
    );

    if (scheduleData) {
      // Create a clean copy of the schedule data to avoid circular references
      const scheduleForNavigation = { ...scheduleData };

      // Store the schedule data in the service for the detail component to access
      this.scheduleDataService.setCurrentSchedule(scheduleForNavigation);

      console.log('Stored schedule data in service:', scheduleForNavigation);

      // Navigate to the detail page
      this.router.navigate(['/schedule', scheduleId]);
    } else {
      // If schedule not found in our loaded schedules, navigate without data
      console.log('Schedule data not found in loaded schedules');
      this.scheduleDataService.clearCurrentSchedule();
      this.router.navigate(['/schedule', scheduleId]);
    }
  }

  getScheduleIcon(type: string): string {
    switch (type) {
      case 'interview':
        return 'fa-solid fa-video';
      case 'assessment':
        return 'fa-solid fa-clipboard-check';
      case 'challenge':
        return 'fa-solid fa-code';
      default:
        return 'fa-solid fa-calendar';
    }
  }

  /**
   * Get the status badge CSS class
   * @param schedule The schedule object
   * @returns The CSS class string
   */
  getStatusBadgeClass(schedule: any): string {
    if (schedule.published) {
      return 'status-published';
    } else {
      return 'status-draft';
    }
  }

  /**
   * Get the status text
   * @param schedule The schedule object
   * @returns The status text
   */
  getStatusText(schedule: any): string {
    if (schedule.published) {
      return 'Published';
    } else {
      return 'Draft';
    }
  }

  // Method removed as we're now using CSS classes
}
