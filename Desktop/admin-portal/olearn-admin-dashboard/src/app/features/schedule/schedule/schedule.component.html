<div class="schedule-container">
  <!-- Header section with tabs and dropdown -->
  <div class="schedule-header">
    <!-- Left side tabs -->
    <div class="schedule-tabs">
      <button class="tab-button" [class.active]="activeTab === 'upcoming'" (click)="setActiveTab('upcoming')">
        Upcoming
      </button>
      <button class="tab-button" [class.active]="activeTab === 'completed'" (click)="setActiveTab('completed')">
        Completed
      </button>
    </div>

    <!-- Right side dropdown button -->
    <div class="schedule-actions">
      <div ngbDropdown class="schedule-dropdown">
        <button class="schedule-btn" id="scheduleDropdown" ngbDropdownToggle>
          New Schedule <i class="fa-solid fa-chevron-down ms-2"></i>
        </button>
        <div ngbDropdownMenu aria-labelledby="scheduleDropdown" class="dropdown-menu-dark">
          <button ngbDropdownItem *ngFor="let type of scheduleTypes"
            (click)="openScheduleModal(type.id, scheduleModal)">
            {{ type.name }}
          </button>
        </div>
      </div>
    </div>
  </div>



  <div class="schedule-header mb-0">
    <div class="schedule-header-content">
      <!-- Left side filter dropdowns -->
      <div class="schedule-filters">
        <!-- Type filter dropdown -->
        <div class="schedule-filter">
          <div ngbDropdown class="filter-dropdown">
            <button class="filter-btn" id="filterDropdown" ngbDropdownToggle>
              <i class="fa-solid fa-filter"></i>
            </button>
            <div ngbDropdownMenu aria-labelledby="filterDropdown" class="dropdown-menu-dark">
              <button ngbDropdownItem *ngFor="let filter of filterOptions" [class.active]="selectedFilter === filter.id"
                (click)="setFilterType(filter.id)">
                {{ filter.name }}
              </button>
            </div>
          </div>
        </div>

        <!-- Batch filter dropdown -->
        <div class="schedule-filter">
          <div ngbDropdown class="filter-dropdown">
            <button class="filter-btn" id="batchFilterDropdown" ngbDropdownToggle>
              <i class="fa-solid fa-users"></i>
            </button>
            <div ngbDropdownMenu aria-labelledby="batchFilterDropdown" class="dropdown-menu-dark batch-filter-dropdown">
              <button ngbDropdownItem [class.active]="!selectedBatchFilter" (click)="setBatchFilter('')">
                All Batches
              </button>
              <div class="dropdown-divider"></div>
              <button ngbDropdownItem *ngFor="let batch of batches"
                [class.active]="selectedBatchFilter === batch.batchId" (click)="setBatchFilter(batch.batchId)">
                {{ batch.name }}
              </button>
            </div>
          </div>
        </div>

        <!-- Sort dropdown -->
        <div class="schedule-filter">
          <div ngbDropdown class="filter-dropdown">
            <button class="filter-btn" id="sortDropdown" ngbDropdownToggle>
              <i class="fa-solid"
                [ngClass]="selectedSortOption === 'dateAdded' ? 'fa-calendar-plus' : 'fa-clock-rotate-left'"></i>
            </button>
            <div ngbDropdownMenu aria-labelledby="sortDropdown" class="dropdown-menu-dark">
              <button ngbDropdownItem [class.active]="selectedSortOption === 'dateAdded'"
                (click)="setSortOption('dateAdded')">
                <i class="fa-solid fa-calendar-plus me-2"></i> Date Added
              </button>
              <button ngbDropdownItem [class.active]="selectedSortOption === 'lastModified'"
                (click)="setSortOption('lastModified')">
                <i class="fa-solid fa-clock-rotate-left me-2"></i> Last Modified
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Right side search input -->
      <div class="schedule-search">
        <div class="search-input-container">
          <input type="text" class="search-input" placeholder="Search by name, email..." [(ngModel)]="searchQuery"
            (input)="onSearchChange()">
          <i class="fa-solid fa-search search-icon"></i>
        </div>
      </div>
    </div>
  </div>



  <!-- Empty state content -->
  <div class="empty-state" *ngIf="!hasSchedules">
    <div class="empty-state-content">
      <!-- Show different message based on whether we're searching or not -->
      <div *ngIf="isSearching">
        <h2>No schedules found</h2>
        <p class="text-muted">
          <i class="fa-solid fa-search-minus me-2"></i>
          Try adjusting your search criteria
        </p>
        <button class="btn btn-outline-secondary mt-3" (click)="clearSearch()">
          <i class="fa-solid fa-times me-2"></i>Clear Search
        </button>
      </div>

      <!-- Default empty state when not searching -->
      <div *ngIf="!isSearching">
        <h2>No schedules yet</h2>
        <div ngbDropdown class="schedule-dropdown mt-3">
          <button class="schedule-btn" id="scheduleDropdownCenter" ngbDropdownToggle>
            Schedule <i class="fa-solid fa-chevron-down ms-2"></i>
          </button>
          <div ngbDropdownMenu aria-labelledby="scheduleDropdownCenter" class="dropdown-menu-dark">
            <button ngbDropdownItem *ngFor="let type of scheduleTypes"
              (click)="openScheduleModal(type.id, scheduleModal)">
              {{ type.name }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Schedule grid -->
  <div class="schedule-grid" *ngIf="hasSchedules">
    <div class="schedule-card" *ngFor="let schedule of schedules" [ngClass]="'schedule-type-' + schedule.type"
      (click)="viewScheduleDetails(schedule.scheduleId)" style="cursor: pointer">
      <!-- Card header -->
      <div class="card-header">
        <div class="schedule-date">
          <div class="start-date">
            <strong>Str Dt.</strong> {{ schedule.scheduleDate | date : "dd MMM yyyy" }} {{ schedule.time ||
            schedule.startTime }}
          </div>
          Scheduled
        </div>
        <!-- Hide action dropdown for completed events -->
        <div ngbDropdown class="action-dropdown" placement="bottom-right" container="body"
          (click)="$event.stopPropagation()" *ngIf="!schedule.isCompleted">
          <button class="action-button" ngbDropdownToggle>
            <i class="fa-solid fa-ellipsis-vertical"></i>
          </button>
          <div ngbDropdownMenu class="dropdown-menu-dark">
            <button ngbDropdownItem (click)="editSchedule(schedule.scheduleId, $event, scheduleModal)">
              <i class="fa-solid fa-pen-to-square"></i> Edit Schedule
            </button>
            <button ngbDropdownItem class="text-danger" (click)="deleteSchedule(schedule.scheduleId, $event)"
              *ngIf="isSuperAdmin()">
              <i class="fa-solid fa-trash"></i> Delete Schedule
            </button>
          </div>
        </div>
      </div>

      <!-- Card content -->
      <div class="card-content">
        <div class="title-container">
          <h3 class="schedule-title">{{ schedule.title }}</h3>
          <div class="schedule-subtitle">
            <div class="icon-container">
              <i [class]="getScheduleIcon(schedule.type)"></i>
            </div>
            <div class="schedule-category">{{ schedule.type }}</div>
          </div>
        </div>
      </div>

      <!-- Card footer -->
      <div class="card-footer">
        <div class="schedule-info">
          <div class="end-date-row">
            <div class="info-item end-date-item">
              <i class="fa-regular fa-calendar-check"></i>
              <span><strong>End Dt.</strong> {{ schedule.endDate ? (schedule.endDate | date : "dd MMM yyyy") :
                (schedule.scheduleDate | date : "dd MMM yyyy") }} {{ schedule.endTime || schedule.time }}</span>
            </div>
            <div class="badges-container">
              <div class="difficulty-badge" [ngClass]="'difficulty-' + schedule.difficulty.toLowerCase()">
                {{ schedule.difficulty }}
              </div>
              <div class="status-badge" [ngClass]="getStatusBadgeClass(schedule)">
                {{ getStatusText(schedule) }}
              </div>
            </div>
          </div>
          <div class="info-item" *ngIf="schedule.fileAttached">
            <i class="fa-solid fa-file-pdf"></i>
            <span>{{ schedule.fileName }}</span>
          </div>
        </div>

        <!-- Batch assignments -->
        <div class="batch-assignments">
          <div class="batch-chips" *ngIf="schedule.assignedBatches && schedule.assignedBatches.length > 0">
            <div class="batch-chip" *ngFor="let batchId of schedule.assignedBatches | slice:0:2">
              <span>{{ getBatchName(batchId) }}</span>
            </div>
            <div class="batch-chip more-chip" *ngIf="schedule.assignedBatches.length > 2">
              <span>+{{ schedule.assignedBatches.length - 2 }} more</span>
            </div>
          </div>
          <!-- No batches message -->
          <div class="no-batches-message" *ngIf="!schedule.assignedBatches || schedule.assignedBatches.length === 0">
            <i class="fa-solid fa-users-slash"></i>
            <span>No batches assigned</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Schedule Modal -->
<ng-template #scheduleModal let-modal>
  <div class="schedule-modal">
    <div class="modal-header">
      <div class="schedule-tabs">
        <button class="tab-button" [class.active]="selectedScheduleType === 'assessment'"
          [class.disabled]="isEditMode && selectedScheduleType !== 'assessment'"
          [disabled]="isEditMode && selectedScheduleType !== 'assessment'"
          (click)="!isEditMode && changeScheduleType('assessment')">
          Skill Assessment
          <span class="edit-indicator" *ngIf="isEditMode && selectedScheduleType === 'assessment'">
            <i class="fa-solid fa-pen-to-square"></i> Editing
          </span>
        </button>
        <button class="tab-button" [class.active]="selectedScheduleType === 'challenge'"
          [class.disabled]="isEditMode && selectedScheduleType !== 'challenge'"
          [disabled]="isEditMode && selectedScheduleType !== 'challenge'"
          (click)="!isEditMode && changeScheduleType('challenge')">
          Code Challenge
          <span class="edit-indicator" *ngIf="isEditMode && selectedScheduleType === 'challenge'">
            <i class="fa-solid fa-pen-to-square"></i> Editing
          </span>
        </button>
        <button class="tab-button" [class.active]="selectedScheduleType === 'interview'"
          [class.disabled]="isEditMode && selectedScheduleType !== 'interview'"
          [disabled]="isEditMode && selectedScheduleType !== 'interview'"
          (click)="!isEditMode && changeScheduleType('interview')">
          Mock Interview
          <span class="edit-indicator" *ngIf="isEditMode && selectedScheduleType === 'interview'">
            <i class="fa-solid fa-pen-to-square"></i> Editing
          </span>
        </button>
      </div>
      <button type="button" class="btn-close" aria-label="Close" (click)="closeModal(modal)"></button>
    </div>

    <div class="modal-body">
      <!-- Assessment Form -->
      <form [formGroup]="assessmentForm" *ngIf="selectedScheduleType === 'assessment'">
        <div class="form-group mb-4">
          <label for="assessmentTitle">Schedule Title</label>
          <input type="text" id="assessmentTitle" class="form-control"
            placeholder="Enter a title for this assessment schedule" formControlName="title" />
        </div>

        <div class="form-group mb-4">
          <label for="skillName">Skill Name</label>
          <input type="text" id="skillName" class="form-control"
            placeholder="Enter the skill to assess (e.g. JavaScript, Python, React)" formControlName="skillName" />
        </div>

        <div class="form-group mb-4">
          <label for="testTopics">Topics</label>
          <textarea id="testTopics" class="form-control"
            placeholder="Enter topics separated by commas (e.g. Arrays, Functions, Objects)" formControlName="testTopic"
            rows="3"></textarea>
          <small class="text-muted">Separate multiple topics with commas</small>
        </div>

        <div class="row mb-4">
          <div class="col-md-6">
            <div class="row">
              <div class="col-md-6">
                <div class="date-fields">
                  <div class="date-field">
                    <label for="scheduleDate">Start Date</label>
                    <div class="form-group">
                      <div class="input-group">
                        <input type="date" id="scheduleDate" class="form-control" formControlName="scheduleDate"
                          [value]="
                            assessmentForm.get('scheduleDate')?.value
                              | date : 'yyyy-MM-dd'
                          " />
                      </div>
                    </div>
                  </div>
                  <div class="date-field">
                    <label for="scheduleEndDate">End Date</label>
                    <div class="form-group">
                      <div class="input-group">

                        <input type="date" id="scheduleEndDate" class="form-control" formControlName="scheduleEndDate"
                          [value]="
                            assessmentForm.get('scheduleEndDate')?.value
                              | date : 'yyyy-MM-dd'
                          " />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="time-fields">
                  <div class="time-field">
                    <label for="scheduleTime">Start Time</label>
                    <div class="form-group">
                      <div class="input-group">

                        <input type="time" id="scheduleTime" class="form-control" formControlName="scheduleTime" />
                      </div>
                    </div>
                  </div>
                  <div class="time-field">
                    <label for="scheduleEndTime">End Time</label>
                    <div class="form-group">
                      <div class="input-group">

                        <input type="time" id="scheduleEndTime" class="form-control"
                          formControlName="scheduleEndTime" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <label>Test Duration</label>
            <div class="form-group">
              <div ngbDropdown class="w-100">
                <button class="btn date-select-btn w-100" ngbDropdownToggle>
                  <i class="fa-solid fa-clock me-2"></i>
                  {{
                  assessmentForm.get("testDuration")?.value
                  }}
                </button>
                <div ngbDropdownMenu class="w-100">
                  <button ngbDropdownItem (click)="setTestDuration('5 minutes')">
                    5 minutes
                  </button>
                  <button ngbDropdownItem (click)="setTestDuration('10 minutes')">
                    10 minutes
                  </button>
                  <button ngbDropdownItem (click)="setTestDuration('15 minutes')">
                    15 minutes
                  </button>
                  <button ngbDropdownItem (click)="setTestDuration('20 minutes')">
                    20 minutes
                  </button>
                  <button ngbDropdownItem (click)="setTestDuration('30 minutes')">
                    30 minutes
                  </button>
                  <button ngbDropdownItem (click)="setTestDuration('45 minutes')">
                    45 minutes
                  </button>
                  <button ngbDropdownItem (click)="setTestDuration('60 minutes')">
                    60 minutes
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-6">
            <label>Difficulty Level</label>
            <div class="difficulty-buttons">
              <button *ngFor="let level of difficultyLevels" type="button" class="btn difficulty-btn" [class.active]="
                  assessmentForm.get('difficultyLevel')?.value === level
                " (click)="setDifficultyLevel(level)">
                <span class="difficulty-indicator" [ngClass]="'difficulty-' + level.toLowerCase()"></span>
                {{ level }}
              </button>
            </div>
          </div>

          <div class="col-md-6">
            <label>Number of Questions</label>
            <div class="question-count-buttons">
              <button *ngFor="let count of questionOptions" type="button" class="btn question-count-btn" [class.active]="
                  assessmentForm.get('numberOfQuestions')?.value === count
                " (click)="setNumberOfQuestions(count)">
                {{ count }}
              </button>
            </div>
          </div>
        </div>

        <div class="form-group mb-4">
          <label>Assign to Batches</label>
          <div class="batch-selection">
            <div ngbDropdown class="w-100">
              <button class="btn date-select-btn w-100" ngbDropdownToggle>
                <i class="fa-solid fa-users me-2"></i>
                <span *ngIf="selectedBatches.length === 0">Select Batches</span>
                <span *ngIf="selectedBatches.length > 0">{{ selectedBatches.length }} Batch(es) Selected</span>
              </button>
              <div ngbDropdownMenu class="w-100 batch-dropdown-menu">
                <div class="batch-checkbox-list">
                  <div class="batch-checkbox-item" *ngFor="let batch of batches">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" [id]="'batch-' + batch.batchId"
                        [checked]="isBatchSelected(+batch.batchId)"
                        (change)="toggleBatchSelection(+batch.batchId, $event)" />
                      <label class="form-check-label" [for]="'batch-' + batch.batchId">
                        <span class="batch-name">{{ batch.name }}</span>
                        <span class="batch-count">{{ batch.studentCount || 0 }} students</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Calendar Invite Option -->
        <!-- <div class="form-group mb-4" *ngIf="!isEditMode">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="sendCalendarInvitesAssessment"
              [(ngModel)]="sendCalendarInvites" />
            <label class="form-check-label" for="sendCalendarInvitesAssessment">
              <i class="fa-solid fa-calendar-plus me-2"></i>
              Send calendar invites to assigned students
            </label>
          </div>
          <small class="text-muted">Students will receive calendar invites with event details</small>
        </div> -->
      </form>

      <!-- Interview Form -->
      <form [formGroup]="interviewForm" *ngIf="selectedScheduleType === 'interview'">
        <div class="form-group mb-4">
          <label for="interviewTitle">Schedule Title</label>
          <input type="text" id="interviewTitle" class="form-control"
            placeholder="Enter a title for this interview schedule" formControlName="title" />
        </div>

        <div class="form-group mb-4">
          <label for="interviewType">Interview Type</label>
          <select class="form-select" id="interviewType" formControlName="interviewType">
            <option value="" disabled selected>Select Interview Type</option>
            <option value="technicalBased">Technical interview</option>
            <option value="scenarioBased">Scenario based interview</option>
            <option value="problemSolving">Problem solving interview</option>
            <option value="hrBased">HR interview</option>
          </select>
        </div>

        <!-- Skill Name field - shown for all interview types -->
        <div class="form-group mb-4">
          <label for="interviewSkillName">Skill Name</label>
          <input type="text" id="interviewSkillName" class="form-control"
            placeholder="Enter the skill name (e.g., Python, JavaScript, Communication)" formControlName="skillName" />
        </div>

        <!-- Topics field - shown for all interview types -->
        <div class="form-group mb-4">
          <label for="interviewTopics">Topics</label>
          <textarea id="interviewTopics" class="form-control"
            placeholder="Enter topics separated by commas (e.g., Arrays, Functions, Objects)"
            formControlName="interviewTopics" rows="3"></textarea>
          <small class="text-muted">Separate multiple topics with commas</small>
        </div>

        <!-- PDF Upload section removed as requested -->
        <!-- We can add it back later if needed -->

        <div class="row mb-4">
          <div class="col-md-6">
            <div class="row">
              <div class="col-md-6">
                <div class="date-fields">
                  <div class="date-field">
                    <label for="interviewScheduleDate">Start Date</label>
                    <div class="form-group">
                      <div class="input-group">

                        <input type="date" id="interviewScheduleDate" class="form-control"
                          formControlName="scheduleDate" [value]="
                            interviewForm.get('scheduleDate')?.value
                              | date : 'yyyy-MM-dd'
                          " />
                      </div>
                    </div>
                  </div>
                  <div class="date-field">
                    <label for="interviewScheduleEndDate">End Date</label>
                    <div class="form-group">
                      <div class="input-group">

                        <input type="date" id="interviewScheduleEndDate" class="form-control"
                          formControlName="scheduleEndDate" [value]="
                            interviewForm.get('scheduleEndDate')?.value
                              | date : 'yyyy-MM-dd'
                          " />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="time-fields">
                  <div class="time-field">
                    <label for="interviewScheduleTime">Start Time</label>
                    <div class="form-group">
                      <div class="input-group">

                        <input type="time" id="interviewScheduleTime" class="form-control"
                          formControlName="scheduleTime" />
                      </div>
                    </div>
                  </div>
                  <div class="time-field">
                    <label for="interviewScheduleEndTime">End Time</label>
                    <div class="form-group">
                      <div class="input-group">

                        <input type="time" id="interviewScheduleEndTime" class="form-control"
                          formControlName="scheduleEndTime" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <label>Difficulty Level</label>
            <div class="difficulty-buttons">
              <button *ngFor="let level of difficultyLevels" type="button" class="btn difficulty-btn" [class.active]="
                  interviewForm.get('difficultyLevel')?.value === level
                " (click)="setDifficultyLevel(level)">
                <span class="difficulty-indicator" [ngClass]="'difficulty-' + level.toLowerCase()"></span>
                {{ level }}
              </button>
            </div>
          </div>
        </div>

        <div class="form-group mb-4">
          <label>Number of Questions</label>
          <div class="question-count-buttons">
            <button *ngFor="let count of interviewQuestionOptions" type="button" class="btn question-count-btn"
              [class.active]="
                interviewForm.get('numberOfQuestions')?.value === count
              " (click)="setNumberOfQuestions(count)">
              {{ count }}
            </button>
          </div>
        </div>

        <!-- Interview Duration -->
        <div class="form-group mb-4">
          <label for="interviewDuration">Interview Duration</label>
          <select class="form-select" id="interviewDuration" formControlName="testDuration">
            <option value="15 minutes">15 minutes</option>
            <option value="30 minutes">30 minutes</option>
            <option value="45 minutes">45 minutes</option>
            <option value="60 minutes">60 minutes</option>
          </select>
        </div>

        <!-- Batch Selection -->
        <div class="form-group mb-4">
          <label>Assign to Batches</label>
          <div class="batch-selection">
            <div ngbDropdown class="w-100">
              <button class="btn date-select-btn w-100" ngbDropdownToggle>
                <i class="fa-solid fa-users me-2"></i>
                <span *ngIf="selectedBatches.length === 0">Select Batches</span>
                <span *ngIf="selectedBatches.length > 0">{{ selectedBatches.length }} Batch(es) Selected</span>
              </button>
              <div ngbDropdownMenu class="w-100 batch-dropdown-menu">
                <div class="batch-checkbox-list">
                  <div class="batch-checkbox-item" *ngFor="let batch of batches">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" [id]="'batch-interview-' + batch.batchId"
                        [checked]="isBatchSelected(+batch.batchId)"
                        (change)="toggleBatchSelection(+batch.batchId, $event)" />
                      <label class="form-check-label" [for]="'batch-interview-' + batch.batchId">
                        <span class="batch-name">{{ batch.name }}</span>
                        <span class="batch-count">{{ batch.studentCount || 0 }} students</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Calendar Invite Option -->
        <!-- <div class="form-group mb-4" *ngIf="!isEditMode">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="sendCalendarInvitesInterview"
              [(ngModel)]="sendCalendarInvites" />
            <label class="form-check-label" for="sendCalendarInvitesInterview">
              <i class="fa-solid fa-calendar-plus me-2"></i>
              Send calendar invites to assigned students
            </label>
          </div>
          <small class="text-muted">Students will receive calendar invites with event details</small>
        </div> -->
      </form>

      <!-- Challenge Form -->
      <form [formGroup]="challengeForm" *ngIf="selectedScheduleType === 'challenge'">
        <div class="form-group mb-4">
          <label for="challengeTitle">Schedule Title</label>
          <input type="text" id="challengeTitle" class="form-control"
            placeholder="Enter a title for this challenge schedule" formControlName="title" />
        </div>

        <div class="form-group mb-4">
          <label for="challengeSkillName">Skill Name</label>
          <input type="text" id="challengeSkillName" class="form-control"
            placeholder="Enter the skill name (e.g., Python, JavaScript)" formControlName="skillName" />
        </div>

        <div class="form-group mb-4">
          <label for="challengeTopic">Challenge Topic</label>
          <textarea id="challengeTopic" class="form-control"
            placeholder="Write topic you'd like to create a code challenge for" formControlName="challengeTopic"
            rows="3"></textarea>
        </div>

        <div class="row mb-4">
          <div class="col-md-6">
            <div class="row">
              <div class="col-md-6">
                <div class="date-fields">
                  <div class="date-field">
                    <label for="challengeScheduleDate">Start Date</label>
                    <div class="form-group">
                      <div class="input-group">

                        <input type="date" id="challengeScheduleDate" class="form-control"
                          formControlName="scheduleDate" [value]="
                            challengeForm.get('scheduleDate')?.value
                              | date : 'yyyy-MM-dd'
                          " />
                      </div>
                    </div>
                  </div>
                  <div class="date-field">
                    <label for="challengeScheduleEndDate">End Date</label>
                    <div class="form-group">
                      <div class="input-group">

                        <input type="date" id="challengeScheduleEndDate" class="form-control"
                          formControlName="scheduleEndDate" [value]="
                            challengeForm.get('scheduleEndDate')?.value
                              | date : 'yyyy-MM-dd'
                          " />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="time-fields">
                  <div class="time-field">
                    <label for="challengeScheduleTime">Start Time</label>
                    <div class="form-group">
                      <div class="input-group">

                        <input type="time" id="challengeScheduleTime" class="form-control"
                          formControlName="scheduleTime" />
                      </div>
                    </div>
                  </div>
                  <div class="time-field">
                    <label for="challengeScheduleEndTime">End Time</label>
                    <div class="form-group">
                      <div class="input-group">

                        <input type="time" id="challengeScheduleEndTime" class="form-control"
                          formControlName="scheduleEndTime" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <label>Difficulty Level</label>
            <div class="difficulty-buttons">
              <button *ngFor="let level of difficultyLevels" type="button" class="btn difficulty-btn" [class.active]="
                  challengeForm.get('difficultyLevel')?.value === level
                " (click)="setDifficultyLevel(level)">
                <span class="difficulty-indicator" [ngClass]="'difficulty-' + level.toLowerCase()"></span>
                {{ level }}
              </button>
            </div>
          </div>
        </div>

        <!-- Batch Selection -->
        <div class="form-group mb-4">
          <label>Assign to Batches</label>
          <div class="batch-selection">
            <div ngbDropdown class="w-100">
              <button class="btn date-select-btn w-100" ngbDropdownToggle>
                <i class="fa-solid fa-users me-2"></i>
                <span *ngIf="selectedBatches.length === 0">Select Batches</span>
                <span *ngIf="selectedBatches.length > 0">{{ selectedBatches.length }} Batch(es) Selected</span>
              </button>
              <div ngbDropdownMenu class="w-100 batch-dropdown-menu">
                <div class="batch-checkbox-list">
                  <div class="batch-checkbox-item" *ngFor="let batch of batches">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" [id]="'batch-challenge-' + batch.batchId"
                        [checked]="isBatchSelected(+batch.batchId)"
                        (change)="toggleBatchSelection(+batch.batchId, $event)" />
                      <label class="form-check-label" [for]="'batch-challenge-' + batch.batchId">
                        <span class="batch-name">{{ batch.name }}</span>
                        <span class="batch-count">{{ batch.studentCount || 0 }} students</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Calendar Invite Option -->
        <!-- <div class="form-group mb-4" *ngIf="!isEditMode">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="sendCalendarInvitesChallenge"
              [(ngModel)]="sendCalendarInvites" />
            <label class="form-check-label" for="sendCalendarInvitesChallenge">
              <i class="fa-solid fa-calendar-plus me-2"></i>
              Send calendar invites to assigned students
            </label>
          </div>
          <small class="text-muted">Students will receive calendar invites with event details</small>
        </div> -->
      </form>
    </div>

    <div class="modal-footer">
      <!-- Create buttons - shown when not in edit mode -->
      <button *ngIf="selectedScheduleType === 'assessment' && !isEditMode" type="button" class="btn schedule-btn"
        [disabled]="assessmentForm.invalid || isLoading" (click)="createAssessment(modal)">
        <i class="fa-solid me-2" [ngClass]="isLoading ? 'fa-spinner fa-spin' : 'fa-robot'"></i>
        {{isLoading ? 'Creating...' : 'Create Assessment'}}
      </button>

      <button *ngIf="selectedScheduleType === 'interview' && !isEditMode" type="button" class="btn schedule-btn"
        [disabled]="interviewForm.invalid || isLoading" (click)="scheduleInterview(modal)">
        <i class="fa-solid me-2" [ngClass]="isLoading ? 'fa-spinner fa-spin' : 'fa-video'"></i>
        {{isLoading ? 'Creating...' : 'Create Interview'}}
      </button>

      <button *ngIf="selectedScheduleType === 'challenge' && !isEditMode" type="button" class="btn schedule-btn"
        [disabled]="challengeForm.invalid || isLoading" (click)="scheduleChallenge(modal)">
        <i class="fa-solid me-2" [ngClass]="isLoading ? 'fa-spinner fa-spin' : 'fa-code'"></i>
        {{isLoading ? 'Creating...' : 'Create Challenge'}}
      </button>

      <!-- Update buttons - shown when in edit mode -->
      <button *ngIf="selectedScheduleType === 'assessment' && isEditMode" type="button"
        class="btn schedule-btn update-btn" id="updateAssessmentBtn" [disabled]="assessmentForm.invalid"
        (click)="updateScheduleFromForm(modal)">
        <i class="fa-solid fa-save me-2"></i>
        Update Assessment
      </button>

      <button *ngIf="selectedScheduleType === 'interview' && isEditMode" type="button"
        class="btn schedule-btn update-btn" id="updateInterviewBtn" [disabled]="interviewForm.invalid"
        (click)="updateScheduleFromForm(modal)">
        <i class="fa-solid fa-save me-2"></i>
        Update Interview
      </button>

      <button *ngIf="selectedScheduleType === 'challenge' && isEditMode" type="button"
        class="btn schedule-btn update-btn" id="updateChallengeBtn" [disabled]="challengeForm.invalid"
        (click)="updateScheduleFromForm(modal)">
        <i class="fa-solid fa-save me-2"></i>
        Update Challenge
      </button>

      <!-- Cancel button - always shown -->
      <button type="button" class="btn btn-secondary" (click)="closeModal(modal)">
        <i class="fa-solid fa-times me-2"></i>Cancel
      </button>
    </div>
  </div>
</ng-template>

<!-- Edit Schedule Modal -->