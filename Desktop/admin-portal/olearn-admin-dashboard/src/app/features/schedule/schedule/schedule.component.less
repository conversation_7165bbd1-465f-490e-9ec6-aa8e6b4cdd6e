// Global modal styles override

.schedule-container {
  padding: 1.5rem;
  min-height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
  background-color: #121212;
  color: #e2e8f0;
}

// Header section with tabs and dropdown
.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  .schedule-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 6px 23px;
    border-radius: 8px;
    background: #1C1C1C;
  }

  // Left side tabs
  .schedule-tabs {
    display: flex;
    gap: 1.5rem;

    .tab-button {
      background: none;
      border: none;
      // padding: 0.5rem 0;
      font-size: 1rem;
      color: #a0aec0;
      position: relative;
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: #e2e8f0;
      }

      &.active {
        color: #ffffff;
        font-weight: 500;
        background-color: #161616;
        border-radius: 12px;
      }
    }
  }

  // Left side filter dropdowns
  .schedule-filters {
    display: flex;
    gap: 0.75rem;

    .schedule-filter {
      .filter-dropdown {
        .filter-btn {
          background-color: transparent;
          color: white;
          border: none;
          padding: 0.5rem 1.25rem;
          font-weight: 500;
          border-radius: 8px;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 0.5rem;

          &:hover {
            background-color: #2a2a2a;
          }

          i {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.7);
          }
        }

        .batch-filter-dropdown {
          max-height: 300px;
          overflow-y: auto;
        }
      }
    }
  }

  // Right side search input
  .schedule-search {
    .search-input-container {
      position: relative;
      width: 300px;

      .search-input {
        width: 100%;
        background-color: #000000;
        color: #ffffff;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 0.5rem 1rem 0.5rem 2.5rem;
        font-size: 0.875rem;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        &:focus {
          outline: none;
          border-color: rgba(255, 255, 255, 0.3);
          box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1);
        }
      }

      .search-icon {
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: rgba(255, 255, 255, 0.5);
        font-size: 0.875rem;
      }
    }
  }

  // Right side actions
  .schedule-actions {
    .schedule-dropdown {
      .schedule-btn {
        background: #2A2A2A;
        color: white;
        border: none;
        padding: 0.5rem 1.25rem;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        gap: 0.5rem;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        i {
          font-size: 0.75rem;
        }
      }
    }
  }
}

// Empty state styles
.empty-state {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;

  .empty-state-content {
    text-align: center;

    h2 {
      color: #a0aec0;
      font-weight: 300;
      margin-bottom: 1.5rem;
      font-size: 2rem;
    }

    p.text-muted {
      color: #7A86F2;
      /* Changed to purple/blue gradient color to match the app's theme */
      font-size: 1rem;
      margin-bottom: 1.5rem;
      font-style: italic;
    }

    .btn-outline-secondary {
      color: #a0aec0;
      border-color: #4a5568;
      background-color: transparent;
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
      border-radius: 6px;
      transition: all 0.2s ease;

      &:hover {
        background-color: #4a5568;
        color: #ffffff;
      }

      i {
        font-size: 0.75rem;
      }
    }

    .schedule-dropdown {
      display: inline-block;

      .schedule-btn {
        background-color: #1a202c; // Match screen background color
        color: #ffffff;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 6px;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        cursor: pointer;

        &:hover {
          background-color: #2d3748;
        }

        i {
          font-size: 0.75rem;
          margin-left: 0.5rem;
        }
      }
    }
  }
}

// Dropdown menu styles
:host ::ng-deep {
  .dropdown-menu-dark {
    min-width: 160px;
    padding: 6px 0;
    margin-top: 5px;
    background-color: #1a202c; // Darker background to match screen color
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

    button {
      padding: 8px 14px;
      color: #e2e8f0;
      font-size: 12px;

      &:hover {
        background-color: rgba(255, 255, 255, 0.08);
      }
    }
  }

  // Fix for dropdown toggle arrow animation
  .dropdown-toggle::after {
    display: none; // Hide default caret
  }
}

// Schedule grid styles
.schedule-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

// Schedule card styles
.schedule-card {
  background-color: #1e1e1e;
  border-radius: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.25s cubic-bezier(0.25, 1, 0.5, 1);
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.03);
  height: 100%;
  min-height: 280px;

  // Hover effects
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    background-color: #1a1a1a; // Slightly darker background on hover
    border: 1px solid rgba(255, 255, 255, 0.1);
  }



  // Card header
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.85rem 1.25rem;
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);

    .schedule-date {
      font-size: 0.75rem;
      color: #a0aec0;
      display: flex;
      flex-direction: column;
      gap: 0.25rem;

      .start-date {
        font-size: 0.8rem;
        color: #e2e8f0;

        strong {
          color: #7A86F2;
          margin-right: 0.25rem;
          font-weight: 600;
        }
      }
    }

    .action-dropdown {
      position: relative;

      .action-button {
        background: none;
        border: none;
        color: #a0aec0;
        cursor: pointer;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background-color 0.2s ease;

        &:hover,
        &:focus {
          background-color: rgba(255, 255, 255, 0.1);
          color: #ffffff;
        }

        &::after {
          display: none; // Hide default dropdown caret
        }
      }

      .dropdown-menu-dark {
        min-width: 180px;
        padding: 6px 0;
        margin-top: 5px;
        background-color: #1a202c;
        border: 1px solid rgba(255, 255, 255, 0.05);
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

        button {
          padding: 8px 14px;
          color: #e2e8f0;
          font-size: 12px;
          display: flex;
          align-items: center;
          gap: 8px;

          i {
            font-size: 12px;
            width: 14px;
            text-align: center;
          }

          &:hover {
            background-color: rgba(255, 255, 255, 0.08);
          }

          &.text-danger {
            color: #fc8181;

            &:hover {
              background-color: rgba(252, 129, 129, 0.1);
            }
          }
        }
      }
    }
  }

  // Card content
  .card-content {
    padding: 1.75rem 1.5rem;
    text-align: left;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .title-container {
      width: 100%;

      .schedule-title {
        font-size: 1.35rem;
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 0.75rem;
        line-height: 1.3;
        // Add a subtle gradient to the title
        background: linear-gradient(90deg, #ffffff, #e2e8f0);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        color: transparent;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .schedule-subtitle {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
      }
    }

    .icon-container {
      width: 32px;
      height: 32px;
      background-color: rgba(255, 255, 255, 0.05);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.08);

      // Subtle glow effect
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
      }

      i {
        font-size: 0.9rem;
        color: #ffffff;
        position: relative;
        z-index: 1;
      }
    }

    // Icon colors based on schedule type
    .schedule-type-interview & .icon-container i {
      color: #9f7aea;
    }

    .schedule-type-assessment & .icon-container i {
      color: #a0aec0;
    }

    .schedule-type-challenge & .icon-container i {
      color: #cbd5e0;
    }

    .schedule-category {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 20px;
      font-size: 0.75rem;
      color: #e2e8f0;
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.2s ease;
    }

    .completed-badge {
      position: absolute;
      top: 56px;
      right: 10px;
      padding: 0.25rem 0.75rem;
      background: rgba(76, 175, 80, 0.15);
      border-radius: 20px;
      font-size: 0.75rem;
      color: #81c784;
      border: 1px solid rgba(76, 175, 80, 0.3);
      font-weight: 500;
      z-index: 5;
    }

    // Category badge colors based on schedule type
    .schedule-type-interview & .schedule-category {
      background: rgba(159, 122, 234, 0.1);
      border-color: rgba(159, 122, 234, 0.3);
      color: #d6bcfa;
    }

    .schedule-type-assessment & .schedule-category {
      background: rgba(160, 174, 192, 0.1);
      border-color: rgba(160, 174, 192, 0.3);
      color: #e2e8f0;
    }

    .schedule-type-challenge & .schedule-category {
      background: rgba(203, 213, 224, 0.1);
      border-color: rgba(203, 213, 224, 0.3);
      color: #edf2f7;
    }
  }

  // Card footer
  .card-footer {
    padding: 1.25rem;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    background-color: rgba(0, 0, 0, 0.1);

    .schedule-info {
      display: flex;
      flex-direction: column;
      margin-bottom: 0.75rem;

      .end-date-row {
        display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    width: 100%;
    flex-direction: column;
    gap: 20px;
      }

      .badges-container {
        display: flex;
        gap: 0.5rem;
        align-items: center;
        flex-wrap: wrap;
      }

      .difficulty-badge {
        padding: 0.2rem 0.5rem;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &.difficulty-easy {
          background-color: rgba(72, 187, 120, 0.15);
          color: #68d391;
          border: 1px solid rgba(72, 187, 120, 0.3);
        }

        &.difficulty-medium {
          background-color: rgba(237, 137, 54, 0.15);
          color: #f6ad55;
          border: 1px solid rgba(237, 137, 54, 0.3);
        }

        &.difficulty-hard {
          background-color: rgba(229, 62, 62, 0.15);
          color: #fc8181;
          border: 1px solid rgba(229, 62, 62, 0.3);
        }
      }

      .status-badge {
        padding: 0.2rem 0.5rem;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &.status-published {
          background-color: rgba(60, 170, 86, 0.15);
          color: #3CAA56;
          border: 1px solid rgba(60, 170, 86, 0.3);
        }

        &.status-draft {
          background-color: rgba(255, 193, 7, 0.15);
          color: #FFC107;
          border: 1px solid rgba(255, 193, 7, 0.3);
        }
      }

      .info-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #a0aec0;
        font-size: 0.75rem;

        i {
          font-size: 0.875rem;
        }

        &.end-date-item {
          flex: 1;
          margin-right: 0.5rem;

          strong {
            color: #7A86F2;
            margin-right: 0.25rem;
            font-weight: 600;
          }

          span {
            font-size: 0.8rem;
            color: #e2e8f0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }

    // Batch assignments
    .batch-assignments {
      margin-top: 0.75rem;
      border-top: 1px solid rgba(255, 255, 255, 0.05);
      padding-top: 0.75rem;

      .batch-chips {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: center;

        .batch-chip {
          background-color: rgba(255, 255, 255, 0.08);
          border-radius: 12px;
          padding: 0.25rem 0.75rem;
          font-size: 0.7rem;
          color: #e2e8f0;
          display: inline-flex;
          align-items: center;
          border: 1px solid rgba(255, 255, 255, 0.05);
          transition: all 0.2s ease;

          &:hover {
            background-color: rgba(255, 255, 255, 0.12);
            transform: translateY(-1px);
          }

          &.more-chip {
            background-color: rgba(122, 134, 242, 0.15);
            color: #7A86F2;
            border-color: rgba(122, 134, 242, 0.3);
          }
        }
      }

      .no-batches-message {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        color: #a0aec0;
        font-size: 0.75rem;
        padding: 0.25rem 0;

        i {
          font-size: 0.875rem;
          color: #fc8181;
          opacity: 0.7;
        }
      }
    }
  }
}


// Media queries for responsive design
@media (max-width: 992px) {
  .schedule-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.25rem;
  }

  .schedule-card {
    min-height: 260px;
  }

  .card-content {
    padding: 1.5rem 1.25rem;
  }

  .end-date-row {
    flex-direction: column;
    align-items: flex-start !important;

    .difficulty-badge {
      margin-top: 0.5rem;
      align-self: flex-end;
    }
  }
}

@media (max-width: 768px) {
  .schedule-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;

    .schedule-filters {
      width: 100%;
      flex-direction: column;
      gap: 0.5rem;
    }

    .schedule-filter,
    .schedule-actions {
      width: 100%;
      margin-top: 0.5rem;

      .filter-dropdown,
      .schedule-dropdown {
        width: 100%;

        .filter-btn,
        .schedule-btn {
          width: 100%;
          justify-content: space-between;
        }
      }
    }
  }

  .empty-state .empty-state-content h2 {
    font-size: 1.5rem;
  }

  .schedule-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 1rem;
  }

  .schedule-card {
    min-height: 240px;

    .card-header {
      padding: 0.75rem 1rem;
    }

    .card-content {
      padding: 1.25rem 1rem;
    }

    .card-footer {
      padding: 1rem;
    }

    .info-item.end-date-item span {
      max-width: 180px;
    }
  }
}

@media (max-width: 576px) {
  .schedule-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .schedule-card {
    max-width: 100%;
    margin: 0 auto;
    min-height: 220px;

    .end-date-row {
      flex-direction: row;
      align-items: center !important;

      .info-item.end-date-item {
        max-width: 65%;

        span {
          max-width: 100%;
          display: block;
        }
      }

      .difficulty-badge {
        margin-top: 0;
      }
    }

    .no-batches-message {
      padding: 0.5rem 0;

      i {
        font-size: 1rem;
      }

      span {
        font-size: 0.8rem;
      }
    }
  }
}



// Schedule Modal Styles
:host ::ng-deep {


  // Drag and drop styles for file upload
  .upload-area {
    border: 2px dashed rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.03);

    &:hover {
      border-color: rgba(255, 255, 255, 0.4);
      background-color: rgba(255, 255, 255, 0.05);
    }

    &.has-file {
      border-color: #3CAA56;
      background-color: rgba(60, 170, 86, 0.05);
    }

    .upload-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.75rem;
      color: #a0aec0;

      i {
        font-size: 2rem;
        color: #a0aec0;
      }

      span {
        font-size: 0.875rem;
      }
    }

    .file-info {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      color: #e2e8f0;

      i {
        font-size: 1.5rem;
        color: #e2e8f0;
      }

      span {
        flex: 1;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .btn-remove-file {
        background: none;
        border: none;
        color: #fc8181;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background-color: rgba(252, 129, 129, 0.1);
        }
      }
    }
  }
}
