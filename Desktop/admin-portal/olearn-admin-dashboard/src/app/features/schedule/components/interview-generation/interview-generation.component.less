.interview-generation-container {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 12px;
  padding: 24px;
  color: #fff;
  min-height: 600px;

  .interview-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .header-info {
      flex: 1;

      h3 {
        font-size: 24px;
        font-weight: 700;
        color: #fff;
        margin: 0 0 8px 0;
      }

      p {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.7);
        margin: 0 0 12px 0;
      }

      .question-counter {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.6);

        .selected-count {
          color: #48BB78;
          font-weight: 600;
        }

        .total-count {
          color: #fff;
          font-weight: 600;
        }
      }
    }

    .interview-stats {
      .action-buttons {
        display: flex;
        gap: 12px;

        .action-btn {
          padding: 12px 20px;
          border: none;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 600;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 8px;
          transition: all 0.2s ease;

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          &.save-draft-btn {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
            border: 1px solid rgba(255, 255, 255, 0.2);

            &:hover:not(:disabled) {
              background-color: rgba(255, 255, 255, 0.15);
            }
          }

          &.publish-btn {
            background-color: #48BB78;
            color: white;

            &:hover:not(:disabled) {
              background-color: #38a169;
            }
          }
        }
      }
    }
  }

  .interview-content-wrapper {
    display: flex;
    gap: 24px;
    min-height: 500px;

    .management-content-left {
      flex: 2;
      
      .question-generator {
        .generator-header {
          margin-bottom: 20px;
          text-align: center;

          h4 {
            font-size: 18px;
            font-weight: 600;
            color: #fff;
            margin: 0 0 8px 0;
          }

          p {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            margin: 0;
          }
        }

        .question-form {
          .generate-section {
            text-align: center;

            .generate-btn {
              padding: 16px 32px;
              border: none;
              border-radius: 8px;
              background-color: #48BB78;
              color: white;
              font-size: 16px;
              font-weight: 600;
              cursor: pointer;
              display: inline-flex;
              align-items: center;
              gap: 10px;
              transition: all 0.2s ease;

              &:hover:not(:disabled) {
                background-color: #38a169;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
              }

              &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
              }

              i {
                font-size: 18px;
              }
            }
          }
        }
      }

      .questions-list {
        .questions-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;

          h4 {
            font-size: 18px;
            font-weight: 600;
            color: #fff;
            margin: 0;
          }

          .generate-more-btn {
            padding: 8px 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;

            &:hover:not(:disabled) {
              background-color: rgba(255, 255, 255, 0.1);
              border-color: #48BB78;
              color: #48BB78;
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }
        }

        .questions-container {
          display: flex;
          flex-direction: column;
          gap: 12px;
          max-height: 400px;
          overflow-y: auto;
          padding-bottom: 10px;
          padding: 20px 20px 100px 0px;

          .question-card {
            background-color: #1a1a1a;
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 16px;
            transition: all 0.3s ease;
            cursor: pointer;

            &:hover {
              border-color: rgba(72, 187, 120, 0.5);
              transform: translateX(4px);
            }

            &.selected {
              border-color: #48BB78;
              background-color: rgba(72, 187, 120, 0.1);
              transform: translateX(8px);
            }

            .question-checkbox {
              margin-bottom: 12px;

              input[type="checkbox"] {
                width: 18px;
                height: 18px;
                accent-color: #48BB78;
                cursor: pointer;
                margin-right: 12px;
              }

              .checkbox-label {
                cursor: pointer;
                display: inline-flex;
                align-items: center;
                gap: 8px;

                .question-tags {
                  display: flex;
                  gap: 6px;

                  .skill-tag {
                    background-color: #48BB78;
                    color: white;
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                    font-weight: 500;
                  }

                  .difficulty-tag {
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                    font-weight: 500;

                    &.easy {
                      background-color: #48BB78;
                      color: white;
                    }

                    &.medium {
                      background-color: #FFC107;
                      color: #1a1a1a;
                    }

                    &.hard {
                      background-color: #F56565;
                      color: white;
                    }
                  }

                  .category-tag {
                    background-color: rgba(255, 255, 255, 0.1);
                    color: rgba(255, 255, 255, 0.8);
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                    font-weight: 500;
                  }
                }
              }
            }

            .question-content {
              .question-text {
                color: rgba(255, 255, 255, 0.9);
                font-size: 14px;
                line-height: 1.5;
                white-space: pre-wrap;
                word-wrap: break-word;
              }
            }
          }
        }

          .add-question-section {
            text-align: center;
            margin-top: 16px;
            padding: 16px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);

            .add-question-btn {
              padding: 12px 24px;
              border: 1px solid #48BB78;
              border-radius: 8px;
              background-color: rgba(72, 187, 120, 0.1);
              color: #48BB78;
              font-size: 14px;
              font-weight: 600;
              cursor: pointer;
              display: inline-flex;
              align-items: center;
              gap: 8px;
              transition: all 0.2s ease;

              &:hover:not(:disabled) {
                background-color: rgba(72, 187, 120, 0.2);
                transform: translateY(-1px);
              }

              &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
                border-color: rgba(255, 255, 255, 0.3);
                color: rgba(255, 255, 255, 0.5);
                background-color: rgba(255, 255, 255, 0.05);
              }

              i {
                font-size: 14px;
              }
            }
          }
        }
      }

      .manual-questions-section {
        margin-top: 30px;
        padding: 20px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        background-color: rgba(255, 255, 255, 0.02);

        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          h4 {
            font-size: 16px;
            font-weight: 600;
            color: #fff;
            margin: 0;
          }

          .add-question-btn {
            padding: 8px 16px;
            border: 1px solid #48BB78;
            border-radius: 6px;
            background-color: rgba(72, 187, 120, 0.1);
            color: #48BB78;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;

            &:hover:not(:disabled) {
              background-color: rgba(72, 187, 120, 0.2);
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
              border-color: rgba(255, 255, 255, 0.3);
              color: rgba(255, 255, 255, 0.5);
              background-color: rgba(255, 255, 255, 0.05);
            }
          }
        }

        .manual-questions-list {
          display: flex;
          flex-direction: column;
          gap: 12px;

          .manual-question-item {
            .question-input-wrapper {
              display: flex;
              gap: 12px;
              align-items: center;

              .manual-question-input {
                flex: 1;
                padding: 12px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 6px;
                background-color: rgba(255, 255, 255, 0.05);
                color: #fff;
                font-size: 14px;
                line-height: 1.5;
                resize: vertical;
                min-height: 80px;

                &::placeholder {
                  color: rgba(255, 255, 255, 0.4);
                }

                &:focus {
                  outline: none;
                  border-color: #48BB78;
                  background-color: rgba(255, 255, 255, 0.08);
                }
              }

              .remove-question-btn {
                padding: 10px;
                border: 1px solid #F56565;
                border-radius: 6px;
                background-color: rgba(245, 101, 101, 0.1);
                color: #F56565;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                min-width: 40px;
                height: 40px;

                &:hover {
                  background-color: rgba(245, 101, 101, 0.2);
                }

                i {
                  font-size: 14px;
                }
              }
            }
          }
        }
      }

      .loading-state {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 300px;

        .loading-animation {
          text-align: center;

          .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-left: 4px solid #48BB78;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
          }

          h3 {
            font-size: 18px;
            font-weight: 600;
            color: #fff;
            margin: 0 0 8px 0;
          }

          p {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            margin: 0;
          }
        }
      }
    }

    .management-content-right {
      flex: 1;
      min-width: 300px;

      .ai-assistant-panel {
        background-color: rgba(255, 255, 255, 0.03);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 20px;
        height: fit-content;
        position: sticky;
        top: 20px;

        .assistant-header {
          margin-bottom: 20px;
          text-align: center;

          h4 {
            font-size: 18px;
            font-weight: 600;
            color: #48BB78;
            margin: 0 0 8px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            i {
              font-size: 20px;
            }
          }

          p {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            margin: 0;
          }
        }

        .assistant-content {
          .tips-section {
            margin-bottom: 24px;

            h5 {
              font-size: 16px;
              font-weight: 600;
              color: #fff;
              margin: 0 0 12px 0;
            }

            .tips-list {
              list-style: none;
              padding: 0;
              margin: 0;

              li {
                padding: 8px 0;
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                line-height: 1.5;
                position: relative;
                padding-left: 20px;

                &:before {
                  content: '•';
                  color: #48BB78;
                  font-weight: bold;
                  position: absolute;
                  left: 0;
                }

                &:not(:last-child) {
                  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
                }
              }
            }
          }

          .stats-section {
            margin-bottom: 24px;
            padding: 16px;
            background-color: rgba(72, 187, 120, 0.05);
            border: 1px solid rgba(72, 187, 120, 0.2);
            border-radius: 8px;

            h5 {
              font-size: 16px;
              font-weight: 600;
              color: #48BB78;
              margin: 0 0 12px 0;
            }

            .stats-grid {
              display: grid;
              grid-template-columns: 1fr;
              gap: 8px;

              .stat-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;

                .stat-label {
                  font-size: 14px;
                  color: rgba(255, 255, 255, 0.7);
                }

                .stat-value {
                  font-size: 16px;
                  font-weight: 600;
                  color: #48BB78;
                }
              }
            }
          }

          .progress-section {
            h5 {
              font-size: 16px;
              font-weight: 600;
              color: #fff;
              margin: 0 0 12px 0;
            }

            .progress-bar {
              width: 100%;
              height: 8px;
              background-color: rgba(255, 255, 255, 0.1);
              border-radius: 4px;
              overflow: hidden;
              margin-bottom: 8px;

              .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #48BB78 0%, #38a169 100%);
                transition: width 0.3s ease;
                border-radius: 4px;
              }
            }

            .progress-text {
              font-size: 14px;
              color: rgba(255, 255, 255, 0.7);
              text-align: center;
              margin: 0;
            }
          }
        }
      }
    }
  }


@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
