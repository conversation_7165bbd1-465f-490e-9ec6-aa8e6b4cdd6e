import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Auth } from '@angular/fire/auth';
import { AiService } from '../../../../core/services/ai.service';
import { FirestoreService } from '../../../../core/services/firestore.service';
import { ToastService } from '../../../../core/services/toast.service';
import { AutoSaveService, AutoSaveRequest } from '../../services/auto-save.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-interview-generation',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './interview-generation.component.html',
  styleUrls: ['./interview-generation.component.less']
})
export class InterviewGenerationComponent implements OnInit, OnD<PERSON>roy {
  @Input() schedule: any;

  // Component state
  generatedQuestions: any[] = [];
  selectedQuestionIndices: number[] = [];
  manualQuestions: any[] = [];
  selectedManualIndices: number[] = [];
  isGenerating: boolean = false;
  isSaving: boolean = false;
  isPublishing: boolean = false;

  // Auto-save subscription
  private autoSaveSubscription: Subscription | null = null;

  constructor(
    private route: ActivatedRoute,
    private auth: Auth,
    private aiService: AiService,
    private firestoreService: FirestoreService,
    private toastService: ToastService,
    private autoSaveService: AutoSaveService
  ) {}

  ngOnInit(): void {
    this.loadExistingContent();
    this.setupAutoSaveListener();
  }

  ngOnDestroy(): void {
    if (this.autoSaveSubscription) {
      this.autoSaveSubscription.unsubscribe();
    }
  }

  /**
   * Setup auto-save listener
   */
  private setupAutoSaveListener(): void {
    this.autoSaveSubscription = this.autoSaveService.autoSaveRequest$.subscribe((request: AutoSaveRequest) => {
      if (request.scheduleType === 'interview' && this.hasUnsavedChanges()) {
        console.log('Interview component: Received auto-save request');
        this.performAutoSave();
      }
    });
  }

  /**
   * Perform auto-save operation
   */
  private performAutoSave(): void {
    if (this.isSaving || this.isPublishing) {
      this.autoSaveService.sendAutoSaveResponse({
        success: false,
        message: 'Save operation already in progress',
        scheduleType: 'interview'
      });
      return;
    }

    console.log('Interview component: Performing auto-save...');

    const scheduleId = this.schedule?.id || this.route.snapshot.params['scheduleId'];
    if (!scheduleId) {
      this.autoSaveService.sendAutoSaveResponse({
        success: false,
        message: 'Schedule information not available',
        scheduleType: 'interview'
      });
      return;
    }

    this.isSaving = true;

    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      generatedQuestions: this.generatedQuestions,
      selectedQuestionIndices: this.selectedQuestionIndices,
      manualQuestions: this.manualQuestions,
      selectedManualIndices: this.selectedManualIndices,
      published: false,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || ''
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isSaving = false;

        // Update the schedule object to reflect the saved state
        if (this.schedule) {
          this.schedule.generatedQuestions = this.generatedQuestions;
          this.schedule.selectedQuestionIndices = this.selectedQuestionIndices;
          this.schedule.manualQuestions = this.manualQuestions;
          this.schedule.selectedManualIndices = this.selectedManualIndices;
          this.schedule.published = false;
          this.schedule.lastUpdatedAt = updateData.lastUpdatedAt;
          this.schedule.lastUpdatedBy = updateData.lastUpdatedBy;
        }

        this.autoSaveService.sendAutoSaveResponse({
          success: true,
          message: 'Interview questions auto-saved successfully',
          scheduleType: 'interview'
        });
      },
      error: (error: any) => {
        console.error('Error during auto-save:', error);
        this.isSaving = false;
        this.autoSaveService.sendAutoSaveResponse({
          success: false,
          message: 'Failed to auto-save interview questions',
          scheduleType: 'interview'
        });
      }
    });
  }

  /**
   * Load existing interview content from schedule
   */
  loadExistingContent(): void {
    if (this.schedule?.generatedQuestions && this.schedule.generatedQuestions.length > 0) {
      this.generatedQuestions = this.schedule.generatedQuestions;
      this.selectedQuestionIndices = this.schedule.selectedQuestionIndices || [];
    }
    if (this.schedule?.manualQuestions && this.schedule.manualQuestions.length > 0) {
      this.manualQuestions = this.schedule.manualQuestions;
      this.selectedManualIndices = this.schedule.selectedManualIndices || [];
    }
  }

  /**
   * Generate interview questions with AI
   */
  generateInterviewQuestions(): void {
    if (!this.schedule) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isGenerating = true;

    const topic = this.schedule.interviewTopic || this.schedule.testTopic || 'General Programming';
    const skill = this.schedule.skillName || 'Programming';

    const maxQuestions = this.schedule?.numberOfQuestions || 5;
    const questionsToGenerate = Math.max(maxQuestions * 2, 10); // Generate at least double the required questions, minimum 10

    const systemInstructions = this.getSystemInstructions(skill, topic, questionsToGenerate);

    const prompt = `Generate ${questionsToGenerate} comprehensive interview questions for ${skill} - ${topic}. Include a mix of theoretical concepts, practical applications, and scenario-based questions.`;

    this.aiService.getGPTResponse(prompt, systemInstructions, 'interview_generation').subscribe({
      next: (response) => {
        try {
          console.log('GPT Response:', response);
          
          const questionsData = JSON.parse(response.reply.choices[0].message.content)?.questions;
          console.log('Parsed questions data:', questionsData);

          if (Array.isArray(questionsData) && questionsData.length > 0) {
            this.generatedQuestions = questionsData;
            this.selectedQuestionIndices = []; // Clear previous selections
            this.isGenerating = false;
            this.toastService.success(`Generated ${questionsData.length} interview questions successfully!`);
          } else {
            throw new Error('Invalid response format - expected array of questions');
          }
        } catch (error) {
          console.error('Error parsing GPT response:', error);
          this.isGenerating = false;
          this.toastService.error('Failed to parse AI response. Please try again.');
        }
      },
      error: (error) => {
        console.error('Error generating questions:', error);
        this.isGenerating = false;
        this.toastService.error('Failed to generate questions. Please try again.');
      }
    });
  }

  /**
   * Toggle question selection
   */
  toggleQuestionSelection(index: number): void {
    const currentIndex = this.selectedQuestionIndices.indexOf(index);
    if (currentIndex > -1) {
      this.selectedQuestionIndices.splice(currentIndex, 1);
    } else {
      // Check if we can add more questions
      const totalSelected = this.getTotalSelectedCount();
      const maxQuestions = this.schedule?.numberOfQuestions || 5;

      if (totalSelected >= maxQuestions) {
        this.toastService.error(`You can only select ${maxQuestions} questions total`);
        return;
      }

      this.selectedQuestionIndices.push(index);
    }
  }

  /**
   * Add a manual question input
   */
  addManualQuestion(): void {
    const newQuestion = {
      question: '',
      difficulty: this.schedule?.difficulty || 'Medium',
      category: 'Manual'
    };

    this.manualQuestions.push(newQuestion);

    // Scroll to the new question after a short delay to ensure DOM is updated
    setTimeout(() => {
      const manualQuestions = document.querySelectorAll('.manual-question-card');
      if (manualQuestions.length > 0) {
        const lastManualQuestion = manualQuestions[manualQuestions.length - 1];
        lastManualQuestion.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

        // Focus on the textarea of the new question
        const textarea = lastManualQuestion.querySelector('textarea') as HTMLTextAreaElement;
        if (textarea) {
          textarea.focus();
        }
      }
    }, 100);
  }

  /**
   * Remove a manual question
   */
  removeManualQuestion(index: number): void {
    this.manualQuestions.splice(index, 1);
    // Remove from selected indices if it was selected
    const selectedIndex = this.selectedManualIndices.indexOf(index);
    if (selectedIndex > -1) {
      this.selectedManualIndices.splice(selectedIndex, 1);
    }
    // Adjust indices for questions that come after the removed one
    this.selectedManualIndices = this.selectedManualIndices.map(i => i > index ? i - 1 : i);
  }

  /**
   * Toggle manual question selection
   */
  toggleManualQuestionSelection(index: number): void {
    const currentIndex = this.selectedManualIndices.indexOf(index);
    if (currentIndex > -1) {
      this.selectedManualIndices.splice(currentIndex, 1);
    } else {
      this.selectedManualIndices.push(index);
    }
  }

  /**
   * Check if manual question is selected
   */
  isManualQuestionSelected(index: number): boolean {
    return this.selectedManualIndices.includes(index);
  }

  /**
   * Delete a generated question
   */
  deleteGeneratedQuestion(index: number): void {
    this.generatedQuestions.splice(index, 1);
    // Remove from selected indices if it was selected
    const selectedIndex = this.selectedQuestionIndices.indexOf(index);
    if (selectedIndex > -1) {
      this.selectedQuestionIndices.splice(selectedIndex, 1);
    }
    // Adjust indices for questions that come after the removed one
    this.selectedQuestionIndices = this.selectedQuestionIndices.map(i => i > index ? i - 1 : i);
  }

  /**
   * Get total selected questions count
   */
  getTotalSelectedCount(): number {
    return this.selectedQuestionIndices.length + this.selectedManualIndices.length;
  }

  /**
   * Get count of non-empty manual questions
   */
  getManualQuestionsCount(): number {
    return this.manualQuestions.filter(q => q.question && q.question.trim()).length;
  }

  /**
   * Get remaining questions count
   */
  getRemainingCount(): number {
    const maxQuestions = this.schedule?.numberOfQuestions || 5;
    return maxQuestions - this.getTotalSelectedCount();
  }

  /**
   * Check if question is selected
   */
  isQuestionSelected(index: number): boolean {
    return this.selectedQuestionIndices.includes(index);
  }

  /**
   * Save draft
   */
  saveDraft(): void {
    if (!this.validateDraftQuestions()) {
      return;
    }

    const scheduleId = this.schedule?.id || this.route.snapshot.params['scheduleId'];
    if (!scheduleId) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isSaving = true;

    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      generatedQuestions: this.generatedQuestions,
      selectedQuestionIndices: this.selectedQuestionIndices,
      manualQuestions: this.manualQuestions,
      selectedManualIndices: this.selectedManualIndices,
      published: false,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || ''
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isSaving = false;
        this.toastService.success('Interview questions saved as draft successfully');
      },
      error: (error: any) => {
        console.error('Error saving draft:', error);
        this.isSaving = false;
        this.toastService.error('Failed to save draft. Please try again.');
      }
    });
  }

  /**
   * Publish questions
   */
  publishQuestions(): void {
    if (!this.validateQuestions()) {
      return;
    }

    const scheduleId = this.schedule?.id || this.route.snapshot.params['scheduleId'];
    if (!scheduleId) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isPublishing = true;

    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    // Get only selected questions for publishing
    const selectedAIQuestions = this.selectedQuestionIndices.map(index => this.generatedQuestions[index]);
    const selectedManualQuestions = this.selectedManualIndices.map(index => this.manualQuestions[index]);

    const updateData = {
      // Only save selected questions when publishing
      generatedQuestions: selectedAIQuestions,
      selectedQuestionIndices: selectedAIQuestions.map((_, index) => index), // Reset indices to 0,1,2...
      manualQuestions: selectedManualQuestions,
      selectedManualIndices: selectedManualQuestions.map((_, index) => index), // Reset indices to 0,1,2...
      published: true,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || ''
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isPublishing = false;

        // Update local state to show only published questions
        this.generatedQuestions = selectedAIQuestions;
        this.selectedQuestionIndices = selectedAIQuestions.map((_, index) => index);
        this.manualQuestions = selectedManualQuestions;
        this.selectedManualIndices = selectedManualQuestions.map((_, index) => index);

        this.toastService.success('Interview questions published successfully');
      },
      error: (error: any) => {
        console.error('Error publishing questions:', error);
        this.isPublishing = false;
        this.toastService.error('Failed to publish questions. Please try again.');
      }
    });
  }

  /**
   * Validate questions for draft (allows any number of questions)
   */
  private validateDraftQuestions(): boolean {
    const totalSelected = this.getTotalSelectedCount();

    if (totalSelected === 0) {
      this.toastService.error('Please select or add at least one question to save as draft');
      return false;
    }

    return true;
  }

  /**
   * Validate questions for publishing (requires exact count)
   */
  private validateQuestions(): boolean {
    const totalSelected = this.getTotalSelectedCount();
    const maxQuestions = this.schedule?.numberOfQuestions || 5;

    if (totalSelected === 0) {
      this.toastService.error('Please select or add at least one question');
      return false;
    }

    if (totalSelected !== maxQuestions) {
      this.toastService.error(`Please select exactly ${maxQuestions} questions (currently selected: ${totalSelected})`);
      return false;
    }

    return true;
  }

  /**
   * Generate more questions (replace existing ones)
   */
  generateMoreQuestions(): void {
    this.generateInterviewQuestions();
  }

  /**
   * Get system instructions based on interview type
   */
  private getSystemInstructions(skill: string, topic: string, questionsToGenerate: number): string {
    const interviewType = this.schedule?.interviewType || 'technicalBased';
    const difficulty = this.schedule?.difficulty || 'Medium';

    if (interviewType === 'hrBased') {
      return this.getHRSystemInstructions(skill, topic, questionsToGenerate, difficulty);
    } else {
      return this.getTechnicalSystemInstructions(skill, topic, questionsToGenerate, difficulty);
    }
  }

  /**
   * Get technical interview system instructions
   */
  private getTechnicalSystemInstructions(skill: string, topic: string, questionsToGenerate: number, difficulty: string): string {
    return `You are a senior technical interviewer at a top-tier technology company with 10+ years of experience in ${skill} development. Your expertise lies in identifying exceptional candidates through strategic questioning that reveals both technical depth and problem-solving capabilities.

CONTEXT:
- Skill Focus: ${skill}
- Topic Area: ${topic}
- Difficulty Level: ${difficulty}
- Interview Setting: Technical assessment for software development role

OBJECTIVE:
Generate exactly ${questionsToGenerate} high-quality technical interview questions that progressively assess the candidate's expertise in ${skill} - ${topic}. Each question should serve a specific purpose in evaluating technical competency.

QUESTION CRITERIA:
1. **Technical Depth**: Questions should probe understanding appropriate for ${difficulty} level
2. **Problem-Solving**: Include scenario-based questions that require analytical thinking at ${difficulty} level
3. **Real-World Application**: Focus on practical situations appropriate for ${difficulty} level developers
4. **Consistent Difficulty**: ALL questions must be at ${difficulty} difficulty level - no mixing of difficulty levels
5. **Specificity**: Avoid generic questions; make them specific to ${topic} within ${skill}

QUESTION TYPES TO INCLUDE:
- **Conceptual**: Test fundamental understanding and theoretical knowledge
- **Practical**: Assess hands-on experience and implementation skills
- **Scenario-Based**: Present real-world problems requiring solution design
- **Debugging**: Questions about troubleshooting and optimization
- **Best Practices**: Evaluate knowledge of industry standards and patterns

DIFFICULTY REQUIREMENT:
Generate ALL questions at ${difficulty} difficulty level. Every question should be marked as "difficulty": "${difficulty}". Do not mix difficulty levels - all questions must match the scheduled difficulty of ${difficulty}.

AVOID:
- Generic programming questions not specific to ${skill}
- Questions with obvious or trivial answers
- Overly complex questions that test memorization over understanding
- Questions that can be easily googled during the interview

Return the response in this exact JSON format:
[
  {
    "question": "Detailed question text that clearly states the problem or concept to discuss...",
    "difficulty": "${difficulty}",
    "category": "Conceptual|Practical|Scenario|Debugging|BestPractices"
  }
]

CRITICAL: Every single question must have "difficulty": "${difficulty}" - do not use any other difficulty level. Ensure each question is interview-appropriate (can be discussed verbally), specific to ${topic}, and designed to reveal the candidate's ${difficulty}-level expertise in ${skill}.`;
  }

  /**
   * Get HR interview system instructions
   */
  private getHRSystemInstructions(skill: string, topic: string, questionsToGenerate: number, difficulty: string): string {
    return `You are an experienced HR Business Partner and talent acquisition specialist with expertise in evaluating candidates for ${skill} development roles. Your focus is on assessing cultural fit, soft skills, communication abilities, and professional competencies.

CONTEXT:
- Role: ${skill} Developer/Engineer
- Focus Area: ${topic} (technical context for role-specific questions)
- Interview Level: ${difficulty}
- Interview Setting: HR/Behavioral assessment

OBJECTIVE:
Generate exactly ${questionsToGenerate} strategic HR interview questions that evaluate the candidate's suitability for a ${skill} development role, with particular attention to ${topic}-related responsibilities.

ASSESSMENT AREAS:
1. **Communication Skills**: Ability to explain technical concepts to non-technical stakeholders
2. **Team Collaboration**: Experience working in development teams and cross-functional groups
3. **Problem-Solving Approach**: How they handle challenges, setbacks, and learning opportunities
4. **Cultural Fit**: Alignment with company values and work environment
5. **Career Motivation**: Understanding of their goals and passion for ${skill} development
6. **Adaptability**: Flexibility in learning new technologies and adapting to change
7. **Leadership Potential**: Initiative, mentoring abilities, and growth mindset

QUESTION TYPES TO INCLUDE:
- **Behavioral**: Past experiences using STAR method evaluation
- **Situational**: Hypothetical scenarios relevant to ${skill} development
- **Motivational**: Understanding career drivers and passion for technology
- **Cultural**: Assessing fit with team dynamics and company values
- **Growth-Oriented**: Learning agility and professional development mindset

ROLE-SPECIFIC CONTEXT:
Since this is for a ${skill} role working with ${topic}, include questions that:
- Assess communication of technical concepts related to ${topic}
- Evaluate collaboration in ${skill} development environments
- Understand their experience with ${topic} projects and challenges
- Gauge their passion for ${skill} technology and continuous learning

DIFFICULTY REQUIREMENT:
Generate ALL questions at ${difficulty} difficulty level. Every question should be marked as "difficulty": "${difficulty}". All questions must match the scheduled difficulty level and be appropriate for ${difficulty}-level candidates.

${this.getHRDifficultyGuidance(difficulty)}

AVOID:
- Purely technical questions (leave those for technical rounds)
- Leading questions that suggest the "right" answer
- Personal questions that could lead to bias or legal issues
- Questions that don't relate to job performance or cultural fit

Return the response in this exact JSON format:
[
  {
    "question": "Open-ended question designed to elicit detailed responses about experiences, motivations, or approaches...",
    "difficulty": "${difficulty}",
    "category": "Behavioral|Situational|Motivational|Cultural|Growth"
  }
]

CRITICAL: Every single question must have "difficulty": "${difficulty}" - do not use any other difficulty level. Focus on questions that help determine if the candidate will thrive in a ${skill} development role and contribute positively to the team culture.`;
  }



  /**
   * Get HR difficulty guidance
   */
  private getHRDifficultyGuidance(difficulty: string): string {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return `Focus on entry-level assessment: basic communication skills, fundamental motivations, and cultural alignment. Questions should be straightforward and help candidates feel comfortable.`;
      case 'medium':
        return `Target mid-level assessment: deeper behavioral examples, complex situational scenarios, and leadership potential. Expect more detailed responses and specific examples.`;
      case 'hard':
        return `Senior-level assessment: strategic thinking, complex team dynamics, mentoring experience, and organizational impact. Questions should probe depth of experience and wisdom.`;
      default:
        return `Balanced assessment covering communication, collaboration, problem-solving, and cultural fit appropriate for the role level.`;
    }
  }

  /**
   * Get dynamic button text for generation
   */
  getGenerateButtonText(): string {
    return `Generate Questions with AI`;
  }

  /**
   * Check if manual questions section should be shown
   */
  shouldShowManualSection(): boolean {
    return this.generatedQuestions && this.generatedQuestions.length > 0;
  }

  /**
   * Check if question counter should be shown
   */
  shouldShowQuestionCounter(): boolean {
    return this.getTotalSelectedCount() > 0 || this.shouldShowManualSection();
  }

  /**
   * Get dynamic loading text
   */
  getLoadingText(): string {
    const skill = this.schedule?.skillName || 'Programming';
    const topic = this.schedule?.testTopic || 'General Programming';

    return `Generating interview questions for ${skill} - ${topic}`;
  }

  /**
   * Check if there are unsaved changes
   */
  hasUnsavedChanges(): boolean {
    // Check if there are generated questions or manual questions that haven't been saved
    return this.generatedQuestions.length > 0 || this.manualQuestions.length > 0;
  }

  /**
   * Check if publish button should be enabled
   */
  canPublish(): boolean {
    const totalSelected = this.getTotalSelectedCount();
    const requiredQuestions = this.schedule?.numberOfQuestions || 5;
    return totalSelected === requiredQuestions;
  }

  /**
   * Get tooltip text for publish button
   */
  getPublishButtonTitle(): string {
    const totalSelected = this.getTotalSelectedCount();
    const requiredQuestions = this.schedule?.numberOfQuestions || 5;

    if (totalSelected === 0) {
      return 'Please select at least one question to publish';
    } else if (totalSelected < requiredQuestions) {
      return `Please select ${requiredQuestions - totalSelected} more question(s). Currently selected: ${totalSelected}/${requiredQuestions}`;
    } else if (totalSelected > requiredQuestions) {
      return `Too many questions selected. Please remove ${totalSelected - requiredQuestions} question(s). Currently selected: ${totalSelected}/${requiredQuestions}`;
    } else {
      return 'Publish selected questions';
    }
  }

  /**
   * Generate questions from AI Assistant prompt
   * This method is called from the parent schedule details component
   */
  generateQuestionsFromAIPrompt(prompt: string): void {
    if (!prompt.trim() || this.isGenerating) {
      return;
    }

    this.isGenerating = true;

    const skill = this.schedule?.skillName || 'Programming';
    const topic = this.schedule?.testTopic || 'General Programming';

    // Create system instructions with full schedule context
    const systemInstructions = this.getSystemInstructionsForPrompt(skill, topic, prompt);

    // Enhanced prompt with schedule context
    const enhancedPrompt = `${prompt}

Schedule Context:
- Skill: ${skill}
- Topic: ${topic}
- Difficulty: ${this.schedule?.difficulty || 'Medium'}
- Interview Type: ${this.schedule?.interviewType || 'technicalBased'}
- Required Questions: ${this.schedule?.numberOfQuestions || 5}

Please generate interview questions based on this context and the user's request.`;

    this.aiService.getGPTResponse(enhancedPrompt, systemInstructions, 'interview_generation').subscribe({
      next: (response) => {
        try {
          console.log('AI Assistant Response:', response);

          const questionsData = JSON.parse(response.reply.choices[0].message.content)?.questions;
          console.log('Parsed questions data:', questionsData);

          if (Array.isArray(questionsData) && questionsData.length > 0) {
            // Add new questions to the beginning (show newly added questions on top)
            this.generatedQuestions = [...questionsData, ...this.generatedQuestions];
            this.isGenerating = false;
            this.toastService.success(`Generated ${questionsData.length} additional interview questions successfully!`);
          } else {
            throw new Error('Invalid response format - expected array of questions');
          }
        } catch (error) {
          console.error('Error parsing AI response:', error);
          this.isGenerating = false;
          this.toastService.error('Failed to parse AI response. Please try again.');
        }
      },
      error: (error) => {
        console.error('Error generating questions from AI prompt:', error);
        this.isGenerating = false;
        this.toastService.error('Failed to generate questions. Please try again.');
      }
    });
  }

  /**
   * Get system instructions for AI Assistant prompts
   */
  private getSystemInstructionsForPrompt(skill: string, topic: string, userPrompt: string): string {
    const interviewType = this.schedule?.interviewType || 'technicalBased';
    const difficulty = this.schedule?.difficulty || 'Medium';

    const baseInstructions = `You are an expert interview question generator. Based on the user's prompt and schedule context, generate relevant interview questions.

SCHEDULE CONTEXT:
- Skill: ${skill}
- Topic: ${topic}
- Difficulty: ${difficulty}
- Interview Type: ${interviewType}

USER REQUEST: ${userPrompt}

INSTRUCTIONS:
1. Generate questions that match the user's specific request
2. Ensure all questions are at ${difficulty} difficulty level
3. Make questions specific to ${skill} and ${topic}
4. Follow ${interviewType === 'hrBased' ? 'HR interview' : 'technical interview'} best practices

RESPONSE FORMAT:
Return a JSON object with this exact structure:
{
  "questions": [
    {
      "question": "Question text here...",
      "difficulty": "${difficulty}",
      "category": "Conceptual|Practical|Scenario|Behavioral|Situational"
    }
  ]
}

CRITICAL: Return ONLY the JSON object, no other text or formatting.`;

    return baseInstructions;
  }
}
