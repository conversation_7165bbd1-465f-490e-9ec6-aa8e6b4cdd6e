import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Auth } from '@angular/fire/auth';
import { AiService } from '../../../../core/services/ai.service';
import { FirestoreService } from '../../../../core/services/firestore.service';
import { ToastService } from '../../../../core/services/toast.service';

@Component({
  selector: 'app-interview-generation',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './interview-generation.component.html',
  styleUrls: ['./interview-generation.component.less']
})
export class InterviewGenerationComponent implements OnInit {
  @Input() schedule: any;

  // Component state
  generatedQuestions: any[] = [];
  selectedQuestionIndices: number[] = [];
  manualQuestions: string[] = [];
  isGenerating: boolean = false;
  isSaving: boolean = false;
  isPublishing: boolean = false;

  constructor(
    private route: ActivatedRoute,
    private auth: Auth,
    private aiService: AiService,
    private firestoreService: FirestoreService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.loadExistingContent();
  }

  /**
   * Load existing interview content from schedule
   */
  loadExistingContent(): void {
    if (this.schedule?.generatedQuestions && this.schedule.generatedQuestions.length > 0) {
      this.generatedQuestions = this.schedule.generatedQuestions;
      this.selectedQuestionIndices = this.schedule.selectedQuestionIndices || [];
    }
    if (this.schedule?.manualQuestions && this.schedule.manualQuestions.length > 0) {
      this.manualQuestions = this.schedule.manualQuestions;
    }
  }

  /**
   * Generate interview questions with AI
   */
  generateInterviewQuestions(): void {
    if (!this.schedule) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isGenerating = true;

    const topic = this.schedule.interviewTopic || this.schedule.testTopic || 'General Programming';
    const skill = this.schedule.skillName || 'Programming';

    const maxQuestions = this.schedule?.numberOfQuestions || 5;
    const questionsToGenerate = Math.max(maxQuestions * 2, 10); // Generate at least double the required questions, minimum 10

    const systemInstructions = `You are an expert technical interviewer for a software development company. Your task is to generate high-quality interview questions that assess a candidate's knowledge and problem-solving skills in ${skill} - ${topic}.

Generate exactly ${questionsToGenerate} diverse interview questions that:
- Test both theoretical knowledge and practical application
- Range from basic to advanced difficulty levels
- Include scenario-based questions
- Cover different aspects of the topic
- Are suitable for verbal discussion in an interview setting
- Help assess the candidate's depth of understanding

Return the response in this exact JSON format:
[
  {
    "question": "Question text here...",
    "difficulty": "Easy|Medium|Hard",
    "category": "Theoretical|Practical|Scenario"
  },
  ...
]`;

    const prompt = `Generate ${questionsToGenerate} comprehensive interview questions for ${skill} - ${topic}. Include a mix of theoretical concepts, practical applications, and scenario-based questions.`;

    this.aiService.getGPTResponse(prompt, systemInstructions, 'interview_generation').subscribe({
      next: (response) => {
        try {
          console.log('GPT Response:', response);
          
          const questionsData = JSON.parse(response.reply.choices[0].message.content)?.questions;
          console.log('Parsed questions data:', questionsData);

          if (Array.isArray(questionsData) && questionsData.length > 0) {
            this.generatedQuestions = questionsData;
            this.selectedQuestionIndices = []; // Clear previous selections
            this.isGenerating = false;
            this.toastService.success(`Generated ${questionsData.length} interview questions successfully!`);
          } else {
            throw new Error('Invalid response format - expected array of questions');
          }
        } catch (error) {
          console.error('Error parsing GPT response:', error);
          this.isGenerating = false;
          this.toastService.error('Failed to parse AI response. Please try again.');
        }
      },
      error: (error) => {
        console.error('Error generating questions:', error);
        this.isGenerating = false;
        this.toastService.error('Failed to generate questions. Please try again.');
      }
    });
  }

  /**
   * Toggle question selection
   */
  toggleQuestionSelection(index: number): void {
    const currentIndex = this.selectedQuestionIndices.indexOf(index);
    if (currentIndex > -1) {
      this.selectedQuestionIndices.splice(currentIndex, 1);
    } else {
      // Check if we can add more questions
      const totalSelected = this.getTotalSelectedCount();
      const maxQuestions = this.schedule?.numberOfQuestions || 5;

      if (totalSelected >= maxQuestions) {
        this.toastService.error(`You can only select ${maxQuestions} questions total`);
        return;
      }

      this.selectedQuestionIndices.push(index);
    }
  }

  /**
   * Add a manual question input
   */
  addManualQuestion(): void {
    const totalSelected = this.getTotalSelectedCount();
    const maxQuestions = this.schedule?.numberOfQuestions || 5;

    if (totalSelected >= maxQuestions) {
      this.toastService.error(`You can only add ${maxQuestions} questions total`);
      return;
    }

    this.manualQuestions.push('');
  }

  /**
   * Remove a manual question
   */
  removeManualQuestion(index: number): void {
    this.manualQuestions.splice(index, 1);
  }

  /**
   * Get total selected questions count
   */
  getTotalSelectedCount(): number {
    return this.selectedQuestionIndices.length + this.getManualQuestionsCount();
  }

  /**
   * Get count of non-empty manual questions
   */
  getManualQuestionsCount(): number {
    return this.manualQuestions.filter(q => q.trim()).length;
  }

  /**
   * Get remaining questions count
   */
  getRemainingCount(): number {
    const maxQuestions = this.schedule?.numberOfQuestions || 5;
    return maxQuestions - this.getTotalSelectedCount();
  }

  /**
   * Check if question is selected
   */
  isQuestionSelected(index: number): boolean {
    return this.selectedQuestionIndices.includes(index);
  }

  /**
   * Save draft
   */
  saveDraft(): void {
    if (!this.validateQuestions()) {
      return;
    }

    const scheduleId = this.schedule?.id || this.route.snapshot.params['scheduleId'];
    if (!scheduleId) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isSaving = true;

    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      generatedQuestions: this.generatedQuestions,
      selectedQuestionIndices: this.selectedQuestionIndices,
      manualQuestions: this.manualQuestions,
      published: false,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || ''
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isSaving = false;
        this.toastService.success('Interview questions saved as draft successfully');
      },
      error: (error: any) => {
        console.error('Error saving draft:', error);
        this.isSaving = false;
        this.toastService.error('Failed to save draft. Please try again.');
      }
    });
  }

  /**
   * Publish questions
   */
  publishQuestions(): void {
    if (!this.validateQuestions()) {
      return;
    }

    const scheduleId = this.schedule?.id || this.route.snapshot.params['scheduleId'];
    if (!scheduleId) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isPublishing = true;

    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      generatedQuestions: this.generatedQuestions,
      selectedQuestionIndices: this.selectedQuestionIndices,
      manualQuestions: this.manualQuestions,
      published: true,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || ''
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isPublishing = false;
        this.toastService.success('Interview questions published successfully');
      },
      error: (error: any) => {
        console.error('Error publishing questions:', error);
        this.isPublishing = false;
        this.toastService.error('Failed to publish questions. Please try again.');
      }
    });
  }

  /**
   * Validate questions before saving/publishing
   */
  private validateQuestions(): boolean {
    const totalSelected = this.getTotalSelectedCount();
    const maxQuestions = this.schedule?.numberOfQuestions || 5;

    if (totalSelected === 0) {
      this.toastService.error('Please select or add at least one question');
      return false;
    }

    if (totalSelected !== maxQuestions) {
      this.toastService.error(`Please select exactly ${maxQuestions} questions (currently selected: ${totalSelected})`);
      return false;
    }

    return true;
  }

  /**
   * Generate more questions (replace existing ones)
   */
  generateMoreQuestions(): void {
    this.generateInterviewQuestions();
  }

  /**
   * Get dynamic button text for generation
   */
  getGenerateButtonText(): string {
    const maxQuestions = this.schedule?.numberOfQuestions || 5;
    const questionsToGenerate = Math.max(maxQuestions * 2, 10);
    return `Generate ${questionsToGenerate} Questions with AI`;
  }

  /**
   * Check if manual questions section should be shown
   */
  shouldShowManualSection(): boolean {
    return this.generatedQuestions && this.generatedQuestions.length > 0;
  }

  /**
   * Check if question counter should be shown
   */
  shouldShowQuestionCounter(): boolean {
    return this.getTotalSelectedCount() > 0 || this.shouldShowManualSection();
  }

  /**
   * Get dynamic loading text
   */
  getLoadingText(): string {
    const maxQuestions = this.schedule?.numberOfQuestions || 5;
    const questionsToGenerate = maxQuestions + 3;
    const skill = this.schedule?.skillName;
    const topic = this.schedule?.testTopic || 'General Programming';

    return `Creating ${questionsToGenerate} interview questions for ${skill} - ${topic}`;
  }
}
