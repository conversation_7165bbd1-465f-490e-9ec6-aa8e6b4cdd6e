.challenge-generation-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #0f0f0f;
  color: #fff;

  .challenge-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .header-info {
      h3 {
        font-size: 20px;
        font-weight: 600;
        color: #fff;
        margin: 0 0 4px 0;
      }

      p {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.6);
        margin: 0;
      }
    }

    .challenge-stats {
      display: flex;
      gap: 16px;

      .action-buttons {
        display: flex;
        gap: 12px;

        .action-btn {
          padding: 10px 20px;
          border: none;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 8px;
          transition: all 0.2s ease;

          &.save-draft-btn {
            background-color: #6c757d;
            color: white;

            &:hover:not(:disabled) {
              background-color: #5a6268;
              transform: translateY(-1px);
            }
          }

          &.publish-btn {
            background-color: #48BB78;
            color: white;

            &:hover:not(:disabled) {
              background-color: #38a169;
              transform: translateY(-1px);
            }
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          i {
            font-size: 14px;
          }
        }
      }

      .stat-item {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);

        i {
          color: #48BB78;
        }
      }
    }
  }



  .challenge-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px 0;

    .challenge-generator {
      .generator-header {
        margin-bottom: 20px;
        text-align: center;

        h4 {
          font-size: 18px;
          font-weight: 600;
          color: #fff;
          margin: 0 0 8px 0;
        }

        p {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.6);
          margin: 0;
        }
      }

      .challenge-form {
        .generate-section {
          text-align: center;

          .generate-btn {
            padding: 16px 32px;
            border: none;
            border-radius: 8px;
            background-color: #48BB78;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.2s ease;

            &:hover:not(:disabled) {
              background-color: #38a169;
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }

            i {
              font-size: 18px;
            }
          }
        }
      }
    }

    .challenges-display {
      .challenges-list {
        margin-bottom: 20px;

        h4 {
          font-size: 18px;
          font-weight: 600;
          color: #fff;
          margin: 0 0 16px 0;
        }

        .challenge-tabs {
          display: flex;
          gap: 8px;
          margin-bottom: 20px;

          .challenge-tab {
            padding: 10px 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background-color: rgba(255, 255, 255, 0.1);
              color: #fff;
            }

            &.active {
              background-color: #48BB78;
              border-color: #48BB78;
              color: white;
            }
          }
        }
      }

      .selected-challenge {
        .challenge-content-display {
          margin-bottom: 20px;

          .challenge-textarea {
            width: 100%;
            min-height: 300px;
            padding: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            background-color: rgba(255, 255, 255, 0.05);
            color: #fff;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;

            &:focus {
              outline: none;
              border-color: #48BB78;
              background-color: rgba(255, 255, 255, 0.08);
            }
          }
        }

        .hints-section {
          margin: 20px 0;
          padding: 16px;
          background-color: rgba(72, 187, 120, 0.1);
          border: 1px solid rgba(72, 187, 120, 0.3);
          border-radius: 8px;

          h5 {
            font-size: 16px;
            font-weight: 600;
            color: #48BB78;
            margin: 0 0 12px 0;
            display: flex;
            align-items: center;
            gap: 8px;

            &::before {
              content: '💡';
              font-size: 18px;
            }
          }

          .hints-list {
            list-style: none;
            padding: 0;
            margin: 0;

            .hint-item {
              display: flex;
              align-items: flex-start;
              gap: 8px;
              padding: 8px 0;
              color: rgba(255, 255, 255, 0.9);
              font-size: 14px;
              line-height: 1.5;

              &:not(:last-child) {
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
              }

              i {
                color: #FFC107;
                font-size: 14px;
                margin-top: 2px;
                flex-shrink: 0;
              }
            }
          }
        }
      }
    }

    .loading-state {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 300px;

      .loading-animation {
        text-align: center;

        .loading-spinner {
          width: 48px;
          height: 48px;
          border: 4px solid rgba(255, 255, 255, 0.1);
          border-left: 4px solid #48BB78;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 20px;
        }

        h3 {
          font-size: 18px;
          font-weight: 600;
          color: #fff;
          margin-bottom: 8px;
        }

        p {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }

    .challenge-display {
      .display-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h4 {
          font-size: 18px;
          font-weight: 600;
          color: #fff;
          margin: 0;
        }

        .action-buttons {
          display: flex;
          gap: 12px;

          .action-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;

            &.save-draft-btn {
              background-color: #6c757d;
              color: white;

              &:hover:not(:disabled) {
                background-color: #5a6268;
                transform: translateY(-1px);
              }
            }

            &.publish-btn {
              background-color: #48BB78;
              color: white;

              &:hover:not(:disabled) {
                background-color: #38a169;
                transform: translateY(-1px);
              }
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }

            i {
              font-size: 14px;
            }
          }
        }
      }

      .challenge-content-display {
        .content-preview {
          background-color: #1a1a1a;
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          padding: 20px;
          margin-bottom: 16px;

          .challenge-text {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            color: #fff;
            white-space: pre-wrap;
            margin: 0;
            word-wrap: break-word;
          }
        }

        .challenge-meta {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);

            i {
              color: #48BB78;
              font-size: 16px;
            }
          }
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}