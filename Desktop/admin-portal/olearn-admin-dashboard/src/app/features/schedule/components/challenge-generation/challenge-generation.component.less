.challenge-generation-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #0f0f0f;
  color: #fff;

  .challenge-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .header-info {
      h3 {
        font-size: 20px;
        font-weight: 600;
        color: #fff;
        margin: 0 0 4px 0;
      }

      p {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.6);
        margin: 0;
      }
    }

    .challenge-stats {
      display: flex;
      gap: 16px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);

        i {
          color: #48BB78;
        }
      }
    }
  }

  .progress-indicator {
    margin: 16px 0;

    .progress-bar {
      width: 100%;
      height: 6px;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background-color: #48BB78;
        transition: width 0.3s ease;
      }
    }

    .progress-text {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.6);
      margin-top: 8px;
    }
  }

  .missing-indicator {
    background-color: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 8px;
    padding: 12px 16px;
    margin: 16px 0;

    .indicator-content {
      .indicator-text {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #FFC107;
        font-size: 14px;
        font-weight: 500;

        i {
          font-size: 16px;
        }
      }

      .missing-hint {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.6);
        font-style: italic;
        margin-top: 8px;
      }
    }
  }

  .challenge-content {
    flex: 1;
    overflow-y: auto;

    .loading-state {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 400px;

      .loading-animation {
        text-align: center;

        .loading-spinner {
          width: 48px;
          height: 48px;
          border: 4px solid rgba(255, 255, 255, 0.1);
          border-left: 4px solid #48BB78;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 20px;
        }

        h3 {
          font-size: 18px;
          font-weight: 600;
          color: #fff;
          margin-bottom: 8px;
        }

        p {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }

    .challenges-list {
      .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #fff;
          margin: 0;
        }

        .action-buttons {
          display: flex;
          gap: 12px;

          .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;

            &.save-draft-btn {
              background-color: #6c757d;
              color: white;

              &:hover:not(:disabled) {
                background-color: #5a6268;
                transform: translateY(-1px);
              }
            }

            &.publish-btn {
              background-color: #48BB78;
              color: white;

              &:hover:not(:disabled) {
                background-color: #38a169;
                transform: translateY(-1px);
              }
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }
        }
      }

      .challenge-items {
        .challenge-item {
          background-color: #1a1a1a;
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          margin-bottom: 16px;
          overflow: hidden;

          .challenge-header-item {
            display: flex;
            align-items: flex-start;
            padding: 16px;
            gap: 12px;

            .challenge-number {
              background-color: #48BB78;
              color: white;
              width: 28px;
              height: 28px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14px;
              font-weight: 600;
              flex-shrink: 0;
            }

            .challenge-info {
              flex: 1;

              .challenge-title {
                font-size: 16px;
                font-weight: 500;
                color: #fff;
                margin-bottom: 8px;
                line-height: 1.4;
              }

              .challenge-meta {
                display: flex;
                align-items: center;
                gap: 8px;
                flex-wrap: wrap;

                .difficulty-badge {
                  padding: 2px 8px;
                  border-radius: 12px;
                  font-size: 11px;
                  font-weight: 500;
                  text-transform: uppercase;

                  &.easy {
                    background-color: rgba(72, 187, 120, 0.2);
                    color: #48BB78;
                  }

                  &.medium {
                    background-color: rgba(255, 193, 7, 0.2);
                    color: #FFC107;
                  }

                  &.hard {
                    background-color: rgba(220, 53, 69, 0.2);
                    color: #DC3545;
                  }
                }

                .language-badge {
                  padding: 2px 8px;
                  border-radius: 12px;
                  font-size: 11px;
                  font-weight: 500;
                  background-color: rgba(108, 117, 125, 0.2);
                  color: #6c757d;
                }

                .topics-list {
                  display: flex;
                  gap: 4px;

                  .topic-tag {
                    padding: 2px 6px;
                    border-radius: 10px;
                    font-size: 10px;
                    background-color: rgba(255, 255, 255, 0.1);
                    color: rgba(255, 255, 255, 0.7);
                  }
                }
              }
            }

            .challenge-actions {
              .delete-btn {
                background: none;
                border: none;
                color: rgba(255, 255, 255, 0.5);
                cursor: pointer;
                padding: 8px;
                border-radius: 4px;
                transition: all 0.2s ease;

                &:hover {
                  color: #DC3545;
                  background-color: rgba(220, 53, 69, 0.1);
                }
              }
            }
          }

          .code-block {
            margin: 0 16px 16px 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            overflow: hidden;

            .code-header {
              background-color: rgba(255, 255, 255, 0.05);
              padding: 8px 12px;
              border-bottom: 1px solid rgba(255, 255, 255, 0.1);

              .code-language {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.6);
                text-transform: uppercase;
              }
            }

            .code-content {
              padding: 12px;
              margin: 0;
              background-color: #000;
              color: #fff;
              font-family: 'Courier New', monospace;
              font-size: 13px;
              line-height: 1.4;
              overflow-x: auto;
            }
          }

          .challenge-options {
            padding: 0 16px 16px 16px;

            .option-item {
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 8px 12px;
              margin-bottom: 8px;
              border-radius: 6px;
              background-color: rgba(255, 255, 255, 0.05);
              border: 1px solid rgba(255, 255, 255, 0.1);

              &.correct {
                background-color: rgba(72, 187, 120, 0.1);
                border-color: rgba(72, 187, 120, 0.3);
              }

              .option-label {
                font-weight: 600;
                color: #48BB78;
                min-width: 20px;
              }

              .option-text {
                flex: 1;
                color: #fff;
                font-size: 14px;
              }

              .correct-icon {
                color: #48BB78;
                font-size: 14px;
              }
            }
          }

          .challenge-explanation {
            padding: 0 16px 16px 16px;

            .explanation-header {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;

              i {
                color: #FFC107;
              }

              span {
                font-weight: 500;
                color: #fff;
                font-size: 14px;
              }
            }

            p {
              color: rgba(255, 255, 255, 0.8);
              font-size: 14px;
              line-height: 1.5;
              margin: 0;
            }
          }
        }
      }
    }
  }
}

  // Content generation options
  .content-generation-options {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    flex: 1;
    max-height: calc(100vh - 400px);
    overflow-y: auto;

    .generation-methods {
      max-width: 500px;
      width: 100%;

      .generation-option {
        padding: 20px;
        margin-bottom: 20px;

        .option-header {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
          margin-bottom: 16px;

          i {
            font-size: 24px;
            color: #48BB78;
          }

          h4 {
            font-size: 18px;
            font-weight: 600;
            color: #fff;
            margin: 0;
          }
        }

        .option-btn {
          padding: 12px 24px;
          border: none;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 8px;
          transition: all 0.2s ease;
          margin: 0 auto;

          &.ai-btn {
            background-color: #48BB78;
            color: white;

            &:hover:not(:disabled) {
              background-color: #38a169;
              transform: translateY(-1px);
            }

            &:disabled {
              background-color: rgba(72, 187, 120, 0.5);
              cursor: not-allowed;
            }
          }

          &.proceed-btn {
            background-color: #3CAA56;
            color: white;
            margin-top: 16px;

            &:hover:not(:disabled) {
              background-color: #2d8a43;
              transform: translateY(-1px);
            }

            &:disabled {
              background-color: rgba(60, 170, 86, 0.5);
              cursor: not-allowed;
            }
          }
        }

        .pdf-upload-area {
          border: 2px dashed rgba(255, 255, 255, 0.3);
          border-radius: 8px;
          padding: 32px 20px;
          margin: 16px 0;
          cursor: pointer;
          transition: all 0.2s ease;
          background-color: rgba(255, 255, 255, 0.02);

          &:hover {
            border-color: rgba(255, 255, 255, 0.5);
            background-color: rgba(255, 255, 255, 0.05);
          }

          &.dragover {
            border-color: #48BB78;
            background-color: rgba(72, 187, 120, 0.1);
          }

          .upload-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;

            i {
              font-size: 48px;
              color: rgba(255, 255, 255, 0.4);
            }

            .upload-text {
              font-size: 16px;
              font-weight: 500;
              color: #fff;
              margin: 0;
            }

            .upload-hint {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.5);
            }
          }

          .file-selected {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background-color: rgba(72, 187, 120, 0.1);
            border-radius: 6px;

            i {
              font-size: 24px;
              color: #48BB78;
            }

            .file-info {
              flex: 1;
              text-align: left;

              .file-name {
                display: block;
                font-size: 14px;
                font-weight: 500;
                color: #fff;
                margin-bottom: 4px;
              }

              .file-size {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.6);
              }
            }

            .remove-file-btn {
              background: none;
              border: none;
              color: rgba(255, 255, 255, 0.6);
              cursor: pointer;
              padding: 4px;
              border-radius: 4px;
              transition: all 0.2s ease;

              &:hover {
                color: #F56565;
                background-color: rgba(245, 101, 101, 0.1);
              }

              i {
                font-size: 16px;
              }
            }
          }
        }
      }

      .or-divider {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 20px 0;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          height: 1px;
          background-color: rgba(255, 255, 255, 0.2);
          z-index: 1;
        }

        span {
          background-color: #0f0f0f;
          color: rgba(255, 255, 255, 0.6);
          padding: 0 16px;
          font-size: 14px;
          font-weight: 500;
          z-index: 2;
          position: relative;
        }
      }
    }
  }

  // Empty state after deletion
  .empty-after-deletion {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    flex: 1;
    max-height: calc(100vh - 400px);
    overflow: hidden;

    .empty-content {
      max-width: 400px;

      .empty-icon {
        font-size: 48px;
        color: #48BB78;
        margin-bottom: 20px;
      }

      h3 {
        font-size: 20px;
        font-weight: 600;
        color: #fff;
        margin-bottom: 12px;
      }

      p {
        color: rgba(255, 255, 255, 0.7);
        font-size: 14px;
        margin-bottom: 24px;
        line-height: 1.5;
      }

      .arrow-pointer {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        color: #48BB78;
        font-size: 14px;
        font-weight: 500;
        animation: pulse 2s infinite;

        i {
          font-size: 16px;
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}
