<div class="interview-generation-container">
  <!-- Header -->
  <div class="interview-header">
    <div class="header-info">
      <h3>Interview Question Generation</h3>
      <p>Create interview questions based on {{schedule?.skillName}} - {{schedule?.testTopic}}</p>
      <div class="question-counter" *ngIf="shouldShowQuestionCounter()">
        <span class="selected-count">{{getTotalSelectedCount()}}</span> /
        <span class="total-count">{{schedule?.numberOfQuestions || 5}}</span> questions selected
      </div>
    </div>
    <div class="interview-stats" *ngIf="getTotalSelectedCount() > 0">
      <div class="action-buttons">
        <button class="action-btn save-draft-btn" (click)="saveDraft()" [disabled]="isSaving || isPublishing">
          <i class="fa-solid" [ngClass]="isSaving ? 'fa-spinner fa-spin' : 'fa-save'"></i>
          {{isSaving ? 'Saving...' : 'Save Draft'}}
        </button>
        <button class="action-btn publish-btn" (click)="publishQuestions()" [disabled]="isSaving || isPublishing">
          <i class="fa-solid" [ngClass]="isPublishing ? 'fa-spinner fa-spin' : 'fa-rocket'"></i>
          {{isPublishing ? 'Publishing...' : 'Publish'}}
        </button>
      </div>
    </div>
  </div>

  <div class="interview-content-wrapper">
    <!-- Left Section - Question Management -->
    <div class="management-content-left">
      <!-- Initial Generation Button -->
      <div class="question-generator" *ngIf="!isGenerating && (!generatedQuestions || generatedQuestions.length === 0)">
        <div class="generator-header">
          <h4>Generate Interview Questions</h4>
          <p>Create interview questions based on {{schedule?.skillName}} - {{schedule?.testTopic}}</p>
        </div>

        <div class="question-form">
          <div class="generate-section">
            <button class="generate-btn" (click)="generateInterviewQuestions()" [disabled]="isGenerating">
              <i class="fa-solid fa-magic"></i>
              {{getGenerateButtonText()}}
            </button>
          </div>
        </div>
      </div>

      <!-- Generated Questions List -->
      <div class="questions-list" *ngIf="generatedQuestions && generatedQuestions.length > 0 && !isGenerating">
        <div class="questions-header">
          <h4>AI Generated Questions ({{generatedQuestions.length}})</h4>
          <button class="generate-more-btn" (click)="generateMoreQuestions()" [disabled]="isGenerating">
            <i class="fa-solid fa-refresh"></i>
            Re-Generate Questions
          </button>
        </div>

        <div class="questions-container">
          <div
            *ngFor="let question of generatedQuestions; let i = index"
            class="question-card"
            [class.selected]="isQuestionSelected(i)">

            <div class="question-checkbox">
              <input
                type="checkbox"
                [id]="'question-' + i"
                [checked]="isQuestionSelected(i)"
                (change)="toggleQuestionSelection(i)">
              <label [for]="'question-' + i" class="checkbox-label">
                <div class="question-tags">
                  <span class="skill-tag">{{schedule?.skillName}}</span>
                  <span class="difficulty-tag" [class]="question.difficulty?.toLowerCase()">{{question.difficulty}}</span>
                  <span class="category-tag">{{question.category}}</span>
                </div>
              </label>
            </div>

            <div class="question-content">
              <div class="question-text">
                {{question.question}}
              </div>
            </div>
          </div>

          <!-- Add Question Button - Inside scrollable container -->
          <div class="add-question-section" *ngIf="!isGenerating">
            <button
              class="add-question-btn"
              (click)="addManualQuestion()"
              [disabled]="getRemainingCount() <= 0">
              <i class="fa-solid fa-plus"></i>
              Add Question
            </button>
          </div>
        </div>
      </div>

      <!-- Manual Questions Section -->
      <div class="manual-questions-section" *ngIf="!isGenerating && manualQuestions.length > 0">
        <div class="section-header">
          <h4>Your Questions</h4>
        </div>
        
        <div class="manual-questions-list" *ngIf="manualQuestions.length > 0">
          <div 
            *ngFor="let question of manualQuestions; let i = index" 
            class="manual-question-item">
            <div class="question-input-wrapper">
              <textarea 
                [(ngModel)]="manualQuestions[i]"
                placeholder="Enter your interview question here..."
                rows="3"
                class="manual-question-input">
              </textarea>
              <button 
                class="remove-question-btn" 
                (click)="removeManualQuestion(i)">
                <i class="fa-solid fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div class="loading-state" *ngIf="isGenerating">
        <div class="loading-animation">
          <div class="loading-spinner"></div>
          <h3>Generating Interview Questions...</h3>
          <p>{{getLoadingText()}}</p>
        </div>
      </div>
    </div>
  </div>
</div>
