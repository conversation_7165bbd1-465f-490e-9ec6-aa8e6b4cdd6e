<div class="challenge-generation-container">
  <!-- Header -->
  <div class="challenge-header">
    <div class="header-info">
      <h3>Challenge Generation</h3>
      <p>Create a coding challenge based on {{schedule?.skillName}} - {{schedule?.challengeTopic || schedule?.testTopic}}</p>
    </div>
    <div class="challenge-stats" *ngIf="(generatedChallenges && generatedChallenges.length > 0) || manualChallengeContent.trim()">
      <div class="action-buttons">
        <button class="action-btn save-draft-btn" (click)="saveDraft()" [disabled]="isSaving || isPublishing">
          <i class="fa-solid" [ngClass]="isSaving ? 'fa-spinner fa-spin' : 'fa-save'"></i>
          {{isSaving ? 'Saving...' : 'Save Draft'}}
        </button>
        <button class="action-btn publish-btn" (click)="publishQuestions()" [disabled]="isSaving || isPublishing">
          <i class="fa-solid" [ngClass]="isPublishing ? 'fa-spinner fa-spin' : 'fa-rocket'"></i>
          {{isPublishing ? 'Publishing...' : 'Publish'}}
        </button>
      </div>
    </div>
  </div>

  <!-- Content Area -->
  <div class="challenge-content">
    <!-- Initial Generation Button -->
    <div class="challenge-generator" *ngIf="!isGenerating && (!generatedChallenges || generatedChallenges.length === 0)">
      <div class="generator-header">
        <h4>Generate Coding Challenges</h4>
        <p>Create coding challenges based on {{schedule?.skillName}} - {{schedule?.challengeTopic || schedule?.testTopic}}</p>
      </div>

      <div class="challenge-form">
        <div class="generate-section">
          <button class="generate-btn" (click)="generateChallengeWithAI()" [disabled]="isGenerating">
            <i class="fa-solid fa-magic"></i>
            Generate 3 Challenges with AI
          </button>
        </div>
      </div>
    </div>

    <!-- Generated Challenges Cards -->
    <div class="challenges-cards" *ngIf="generatedChallenges && generatedChallenges.length > 0 && !isGenerating">
      <div class="challenges-grid">
        <div
          *ngFor="let challenge of generatedChallenges; let i = index"
          class="challenge-card"
          [class.selected]="selectedChallengeIndex === i">

          <div class="card-header">
            <div class="card-checkbox">
              <input
                type="radio"
                [id]="'challenge-' + i"
                name="selectedChallenge"
                [value]="i"
                [(ngModel)]="selectedChallengeIndex"
                (change)="selectChallenge(i)">
              <label [for]="'challenge-' + i" class="checkbox-label">
                <span class="skill-tag">{{schedule?.skillName}}</span>
                <span class="topic-tag">{{schedule?.challengeTopic || schedule?.testTopic}}</span>
              </label>
            </div>
          </div>

          <div class="card-content">
            <div class="challenge-preview">
              {{challenge.challenge | slice:0:200}}{{challenge.challenge.length > 200 ? '...' : ''}}
            </div>
          </div>
        </div>
      </div>

      <!-- Generate More Button -->
      <div class="generate-more-section">
        <button class="generate-more-btn" (click)="generateMoreChallenges()" [disabled]="isGenerating">
          <i class="fa-solid fa-refresh"></i>
          Generate 3 More
        </button>
      </div>
    </div>

    <!-- Manual Challenge Input -->
    <div class="manual-challenge-section" *ngIf="!isGenerating">
      <div class="section-header">
        <h4>Or Create Your Own Challenge</h4>
      </div>

      <div class="manual-input">
        <textarea
          [(ngModel)]="manualChallengeContent"
          placeholder="Enter your custom challenge question here..."
          rows="8"
          class="manual-textarea">
        </textarea>
      </div>
    </div>

    <!-- Loading State -->
    <div class="loading-state" *ngIf="isGenerating">
      <div class="loading-animation">
        <div class="loading-spinner"></div>
        <h3>Generating Challenges...</h3>
        <p>Creating 3 coding challenges for {{schedule?.skillName}} - {{schedule?.challengeTopic || schedule?.testTopic}}</p>
      </div>
    </div>
  </div>
</div>
