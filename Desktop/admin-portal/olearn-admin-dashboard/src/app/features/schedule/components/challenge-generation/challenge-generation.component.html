<div class="challenge-generation-container">
  <!-- Header -->
  <div class="challenge-header">
    <div class="header-info">
      <h3>Coding Challenges</h3>
      <p>Generate programming challenges and algorithmic problems</p>
    </div>
    <div class="challenge-stats" *ngIf="generatedQuestions.length > 0">
      <span class="stat-item">
        <i class="fa-solid fa-code"></i>
        {{generatedQuestions.length}} Challenges
      </span>
      <span class="stat-item">
        <i class="fa-solid fa-target"></i>
        {{schedule?.difficulty || 'Medium'}} Level
      </span>
    </div>
  </div>

  <!-- Progress Indicator -->
  <div class="progress-indicator" *ngIf="schedule?.numberOfQuestions">
    <div class="progress-bar">
      <div class="progress-fill" [style.width.%]="(generatedQuestions.length / schedule.numberOfQuestions) * 100"></div>
    </div>
    <div class="progress-text">
      {{generatedQuestions.length}} / {{schedule.numberOfQuestions}} challenges generated
    </div>
  </div>

  <!-- Missing Questions Indicator -->
  <div class="missing-indicator" *ngIf="generatedQuestions.length > 0 && generatedQuestions.length < (schedule?.numberOfQuestions || 10)">
    <div class="indicator-content">
      <div class="indicator-text">
        <i class="fa-solid fa-exclamation-triangle"></i>
        <span>{{(schedule?.numberOfQuestions || 10) - generatedQuestions.length}} challenges missing from the required {{schedule?.numberOfQuestions || 10}}</span>
      </div>
      <div class="missing-hint">
        <span>Use the AI Assistant on the right to generate more challenges</span>
      </div>
    </div>
  </div>

  <!-- Content Area -->
  <div class="challenge-content">
    <!-- Loading State -->
    <div class="loading-state" *ngIf="isGenerating">
      <div class="loading-animation">
        <div class="loading-spinner"></div>
        <h3>Generating Coding Challenges...</h3>
        <p>Creating algorithmic problems and programming challenges</p>
      </div>
    </div>

    <!-- Generated Challenges List -->
    <div class="challenges-list" *ngIf="generatedQuestions.length > 0 && !isGenerating">
      <div class="list-header">
        <h4>Generated Challenges</h4>
        <div class="action-buttons">
          <button class="action-btn save-draft-btn" (click)="saveDraft()" [disabled]="isSaving || isPublishing">
            <i class="fa-solid" [ngClass]="isSaving ? 'fa-spinner fa-spin' : 'fa-save'"></i>
            {{isSaving ? 'Saving...' : 'Save Draft'}}
          </button>
          <button class="action-btn publish-btn" (click)="publishQuestions()" [disabled]="isSaving || isPublishing">
            <i class="fa-solid" [ngClass]="isPublishing ? 'fa-spinner fa-spin' : 'fa-rocket'"></i>
            {{isPublishing ? 'Publishing...' : 'Publish'}}
          </button>
        </div>
      </div>

      <div class="challenge-items">
        <div class="challenge-item" *ngFor="let question of generatedQuestions; let i = index">
          <div class="challenge-header-item">
            <div class="challenge-number">{{i + 1}}</div>
            <div class="challenge-info">
              <div class="challenge-title">{{question.question}}</div>
              <div class="challenge-meta">
                <span class="difficulty-badge" [ngClass]="question.difficulty.toLowerCase()">
                  {{question.difficulty}}
                </span>
                <span class="language-badge" *ngIf="question.language">
                  {{question.language}}
                </span>
                <span class="topics-list">
                  <span class="topic-tag" *ngFor="let topic of question.topicsCovered">{{topic}}</span>
                </span>
              </div>
            </div>
            <div class="challenge-actions">
              <button class="action-btn delete-btn" (click)="deleteQuestion(question.id)" title="Delete Challenge">
                <i class="fa-solid fa-trash"></i>
              </button>
            </div>
          </div>

          <!-- Code Block -->
          <div class="code-block" *ngIf="question.code">
            <div class="code-header">
              <span class="code-language">{{question.language || 'code'}}</span>
            </div>
            <pre class="code-content"><code>{{question.code}}</code></pre>
          </div>

          <!-- Options -->
          <div class="challenge-options">
            <div class="option-item" *ngFor="let option of question.options; let optIndex = index" 
                 [ngClass]="{'correct': option.isCorrect}">
              <span class="option-label">{{String.fromCharCode(65 + optIndex)}}.</span>
              <span class="option-text">{{option.text}}</span>
              <i class="fa-solid fa-check correct-icon" *ngIf="option.isCorrect"></i>
            </div>
          </div>

          <!-- Explanation -->
          <div class="challenge-explanation" *ngIf="question.explanation">
            <div class="explanation-header">
              <i class="fa-solid fa-lightbulb"></i>
              <span>Solution Approach</span>
            </div>
            <p>{{question.explanation}}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State for Initial Generation -->
    <div class="content-generation-options" *ngIf="generatedQuestions.length === 0 && !isGenerating && !hasEverHadQuestions()">
      <div class="generation-methods">
        
        <!-- PDF Upload Option -->
        <div class="generation-option">
          <div class="option-header">
            <i class="fa-solid fa-file-pdf"></i>
            <h4>Upload PDF</h4>
          </div>
          
          <!-- PDF Upload Area -->
          <div class="pdf-upload-area" 
               [class.dragover]="isDragOver"
               (dragover)="onDragOver($event)"
               (dragleave)="onDragLeave($event)"
               (drop)="onDrop($event)"
               (click)="triggerFileInput()">
            <input #fileInput 
                   type="file" 
                   accept=".pdf"
                   (change)="onFileSelected($event)"
                   style="display: none;">
            
            <div class="upload-content" *ngIf="!selectedFile">
              <i class="fa-solid fa-cloud-upload-alt"></i>
              <p class="upload-text">Drag & drop PDF here or click to browse</p>
              <span class="upload-hint">Supports PDF files up to 10MB</span>
            </div>
            
            <div class="file-selected" *ngIf="selectedFile">
              <i class="fa-solid fa-file-pdf"></i>
              <div class="file-info">
                <span class="file-name">{{selectedFile.name}}</span>
                <span class="file-size">{{formatFileSize(selectedFile.size)}}</span>
              </div>
              <button class="remove-file-btn" (click)="removeFile($event)">
                <i class="fa-solid fa-times"></i>
              </button>
            </div>
          </div>
          
          <!-- Proceed Button -->
          <button class="option-btn proceed-btn" 
                  *ngIf="selectedFile"
                  (click)="generateQuestionsFromPDF()" 
                  [disabled]="isGeneratingFromPDF">
            <i class="fa-solid" [ngClass]="isGeneratingFromPDF ? 'fa-spinner fa-spin' : 'fa-arrow-right'"></i>
            {{isGeneratingFromPDF ? 'Processing PDF...' : 'Proceed with PDF'}}
          </button>
        </div>

        <!-- OR Divider -->
        <div class="or-divider">
          <span>or</span>
        </div>

        <!-- AI Generation Option -->
        <div class="generation-option">
          <button class="option-btn ai-btn" (click)="generateQuestions()" [disabled]="isGenerating">
            <i class="fa-solid fa-magic"></i>
            Generate Challenges with AI
          </button>
        </div>
      </div>
    </div>

    <!-- Empty State After Deletion -->
    <div class="empty-after-deletion" *ngIf="generatedQuestions.length === 0 && !isGenerating && hasEverHadQuestions()">
      <div class="empty-content">
        <div class="empty-icon">
          <i class="fa-solid fa-code"></i>
        </div>
        <h3>No Challenges Available</h3>
        <p>All challenges have been deleted. Use the AI Assistant on the right to generate new coding challenges.</p>
        <div class="arrow-pointer">
          <i class="fa-solid fa-arrow-right"></i>
          <span>Try the AI Assistant</span>
        </div>
      </div>
    </div>
  </div>
</div>
