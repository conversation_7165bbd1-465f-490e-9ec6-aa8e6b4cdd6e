<div class="challenge-generation-container">
  <!-- Header -->
  <div class="challenge-header">
    <div class="header-info">
      <h3>Challenge Generation</h3>
      <p>Create a coding challenge based on {{schedule?.skillName}} - {{schedule?.challengeTopic || schedule?.testTopic}}</p>
    </div>
    <div class="challenge-stats" *ngIf="generatedChallenges && generatedChallenges.length > 0">
      <div class="action-buttons">
        <button class="action-btn save-draft-btn" (click)="saveDraft()" [disabled]="isSaving || isPublishing">
          <i class="fa-solid" [ngClass]="isSaving ? 'fa-spinner fa-spin' : 'fa-save'"></i>
          {{isSaving ? 'Saving...' : 'Save Draft'}}
        </button>
        <button class="action-btn publish-btn" (click)="publishQuestions()" [disabled]="isSaving || isPublishing">
          <i class="fa-solid" [ngClass]="isPublishing ? 'fa-spinner fa-spin' : 'fa-rocket'"></i>
          {{isPublishing ? 'Publishing...' : 'Publish'}}
        </button>
      </div>
    </div>
  </div>

  <!-- Content Area -->
  <div class="challenge-content">
    <!-- Challenge Generation Form -->
    <div class="challenge-generator" *ngIf="!isGenerating && (!generatedChallenges || generatedChallenges.length === 0)">
      <div class="generator-header">
        <h4>Generate Coding Challenges</h4>
        <p>Create coding challenges based on {{schedule?.skillName}} - {{schedule?.challengeTopic || schedule?.testTopic}}</p>
      </div>

      <div class="challenge-form">
        <div class="generate-section">
          <button class="generate-btn" (click)="generateChallengeWithAI()" [disabled]="isGenerating">
            <i class="fa-solid fa-magic"></i>
            Generate 3 Challenges with AI
          </button>
        </div>
      </div>
    </div>

    <!-- Generated Challenges Display -->
    <div class="challenges-display" *ngIf="generatedChallenges && generatedChallenges.length > 0 && !isGenerating">
      <div class="challenges-list">
        <h4>Generated Challenges ({{generatedChallenges.length}})</h4>
        <div class="challenge-tabs">
          <button
            *ngFor="let challenge of generatedChallenges; let i = index"
            class="challenge-tab"
            [class.active]="selectedChallenge === challenge"
            (click)="selectChallenge(challenge)">
            Challenge {{i + 1}}
          </button>
        </div>
      </div>

      <!-- Selected Challenge Display -->
      <div class="selected-challenge" *ngIf="selectedChallenge">
        <div class="challenge-content-display">
          <textarea
            [(ngModel)]="selectedChallenge.challenge"
            rows="12"
            class="challenge-textarea">
          </textarea>
        </div>

        <!-- Hints Section -->
        <div class="hints-section" *ngIf="selectedChallenge.hints && selectedChallenge.hints.length > 0">
          <h5>Hints for Students:</h5>
          <ul class="hints-list">
            <li *ngFor="let hint of selectedChallenge.hints" class="hint-item">
              <i class="fa-solid fa-lightbulb"></i>
              {{hint}}
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div class="loading-state" *ngIf="isGenerating">
      <div class="loading-animation">
        <div class="loading-spinner"></div>
        <h3>Generating Challenges...</h3>
        <p>Creating 3 coding challenges for {{schedule?.skillName}} - {{schedule?.challengeTopic || schedule?.testTopic}}</p>
      </div>
    </div>
  </div>
</div>
