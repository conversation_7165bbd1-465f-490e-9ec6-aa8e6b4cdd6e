<div class="challenge-generation-container">
  <!-- Header -->
  <div class="challenge-header">
    <div class="header-info">
      <h3>Challenge Generation</h3>
      <p>Create coding challenges for {{schedule?.skillName}} - {{schedule?.testTopic}}</p>
    </div>
    <div class="challenge-stats" *ngIf="challengeContent">
      <span class="stat-item">
        <i class="fa-solid fa-code"></i>
        Challenge Ready
      </span>
      <span class="stat-item">
        <i class="fa-solid fa-target"></i>
        {{schedule?.difficulty || 'Medium'}} Level
      </span>
    </div>
  </div>

  <!-- Content Area -->
  <div class="challenge-content">
    <!-- Challenge Generation Form -->
    <div class="challenge-generator" *ngIf="!isGenerating">
      <div class="generator-header">
        <h4>Generate Challenge Question</h4>
        <p>Create a coding challenge based on {{schedule?.skillName}} - {{schedule?.testTopic}}</p>
      </div>

      <div class="challenge-form">
        <div class="form-group">
          <label for="challengeTextarea">Challenge Question</label>
          <textarea
            id="challengeTextarea"
            [(ngModel)]="challengeContent"
            placeholder="Enter your challenge question or let AI generate one for you..."
            rows="8"
            class="challenge-textarea">
          </textarea>
        </div>

        <div class="generate-section">
          <button class="generate-btn" (click)="generateChallengeWithAI()" [disabled]="isGenerating">
            <i class="fa-solid fa-magic"></i>
            Generate with AI
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div class="loading-state" *ngIf="isGenerating">
      <div class="loading-animation">
        <div class="loading-spinner"></div>
        <h3>Generating Challenge Question...</h3>
        <p>Creating coding challenge for {{schedule?.skillName}}</p>
      </div>
    </div>

    <!-- Challenge Display with Actions -->
    <div class="challenge-display" *ngIf="challengeContent && !isGenerating">
      <div class="display-header">
        <h4>Challenge Question</h4>
        <div class="action-buttons">
          <button class="action-btn save-draft-btn" (click)="saveDraft()" [disabled]="isSaving || isPublishing">
            <i class="fa-solid" [ngClass]="isSaving ? 'fa-spinner fa-spin' : 'fa-save'"></i>
            {{isSaving ? 'Saving...' : 'Save Draft'}}
          </button>
          <button class="action-btn publish-btn" (click)="publishQuestions()" [disabled]="isSaving || isPublishing">
            <i class="fa-solid" [ngClass]="isPublishing ? 'fa-spinner fa-spin' : 'fa-rocket'"></i>
            {{isPublishing ? 'Publishing...' : 'Publish'}}
          </button>
        </div>
      </div>

      <div class="challenge-content-display">
        <div class="content-preview">
          <pre class="challenge-text">{{challengeContent}}</pre>
        </div>

        <div class="challenge-meta">
          <span class="meta-item">
            <i class="fa-solid fa-code"></i>
            {{schedule?.skillName}}
          </span>
          <span class="meta-item">
            <i class="fa-solid fa-tag"></i>
            {{schedule?.testTopic}}
          </span>
          <span class="meta-item">
            <i class="fa-solid fa-signal"></i>
            {{schedule?.difficulty || 'Medium'}}
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
