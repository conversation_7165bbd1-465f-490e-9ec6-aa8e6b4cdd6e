<div class="challenge-generation-container">
  <!-- Header -->
  <div class="challenge-header">
    <div class="header-info">
      <h3>Challenge Generation</h3>
      <p>Create a coding challenge based on {{schedule?.skillName}} - {{schedule?.challengeTopic || schedule?.testTopic}}</p>
    </div>
    <div class="challenge-stats" *ngIf="challengeContent">
      <div class="action-buttons">
        <button class="action-btn save-draft-btn" (click)="saveDraft()" [disabled]="isSaving || isPublishing">
          <i class="fa-solid" [ngClass]="isSaving ? 'fa-spinner fa-spin' : 'fa-save'"></i>
          {{isSaving ? 'Saving...' : 'Save Draft'}}
        </button>
        <button class="action-btn publish-btn" (click)="publishQuestions()" [disabled]="isSaving || isPublishing">
          <i class="fa-solid" [ngClass]="isPublishing ? 'fa-spinner fa-spin' : 'fa-rocket'"></i>
          {{isPublishing ? 'Publishing...' : 'Publish'}}
        </button>
      </div>
    </div>
  </div>

  <!-- Content Area -->
  <div class="challenge-content">
    <!-- Challenge Generation Form -->
    <div class="challenge-generator" *ngIf="!isGenerating">
    
      <div class="challenge-form">
        <div class="form-group">
          <label for="challengeTextarea">Challenge Question</label>
          <textarea
            id="challengeTextarea"
            [(ngModel)]="challengeContent"
            placeholder="Enter your challenge question or let AI generate one for you..."
            rows="8"
            class="challenge-textarea">
          </textarea>
        </div>

        <!-- Hints Section -->
        <div class="hints-section" *ngIf="challengeHints && challengeHints.length > 0">
          <h5>Hints for Students:</h5>
          <ul class="hints-list">
            <li *ngFor="let hint of challengeHints" class="hint-item">
              <i class="fa-solid fa-lightbulb"></i>
              {{hint}}
            </li>
          </ul>
        </div>

        <div class="generate-section">
          <button class="generate-btn" (click)="generateChallengeWithAI()" [disabled]="isGenerating">
            <i class="fa-solid fa-magic"></i>
            Generate with AI
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div class="loading-state" *ngIf="isGenerating">
      <div class="loading-animation">
        <div class="loading-spinner"></div>
        <h3>Generating Challenge Question...</h3>
        <p>Creating coding challenge for {{schedule?.skillName}} - {{schedule?.challengeTopic || schedule?.testTopic}}</p>
      </div>
    </div>


  </div>
</div>
