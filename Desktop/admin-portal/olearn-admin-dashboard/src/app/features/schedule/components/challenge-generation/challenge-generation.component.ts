import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { ToastService } from '../../../../core/services/toast.service';
import { AiService } from '../../../../core/services/ai.service';
import { FirestoreService } from '../../../../core/services/firestore.service';
import { Auth } from '@angular/fire/auth';
import { ActivatedRoute } from '@angular/router';

// Simplified interface for challenge content
export interface ChallengeData {
  content: string;
  skillName: string;
  testTopic: string;
  difficulty: string;
  lastUpdatedAt?: Date;
  lastUpdatedBy?: string;
  published?: boolean;
}

@Component({
  selector: 'app-challenge-generation',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './challenge-generation.component.html',
  styleUrls: ['./challenge-generation.component.less']
})
export class ChallengeGenerationComponent implements OnInit, OnDestroy {
  @Input() schedule: any;

  // Component state
  challengeContent: string = '';
  challengeHints: string[] = [];
  isGenerating: boolean = false;
  isSaving: boolean = false;
  isPublishing: boolean = false;

  private destroy$ = new Subject<void>();



  constructor(
    private toastService: ToastService,
    private aiService: AiService,
    private firestoreService: FirestoreService,
    private auth: Auth,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.loadExistingContent();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load existing challenge content from schedule
   */
  loadExistingContent(): void {
    if (this.schedule?.challengeContent) {
      this.challengeContent = this.schedule.challengeContent;
    }
    if (this.schedule?.challengeHints) {
      this.challengeHints = this.schedule.challengeHints;
    }
  }

  /**
   * Generate challenge with AI
   */
  generateChallengeWithAI(): void {
    if (!this.schedule) {
      this.toastService.error('Schedule data not available');
      return;
    }

    this.isGenerating = true;

    const skillName = this.schedule.skillName || 'Programming';
    const topics = this.schedule.challengeTopic || this.schedule.testTopic || 'General Programming';
    const difficulty = this.schedule.difficulty || 'Medium';

    const systemInstruction = `You are an expert coding challenge creator for EdTech companies. Create post-class coding challenges for students to practice immediately after their lesson.

CONTEXT: Students just completed a class on ${skillName} - ${topics}. Create a challenge that reinforces these concepts.

CRITICAL: You must return ONLY a valid JSON object in this EXACT format:

{
  "challenge": "Problem: [Title]\n\nWrite a [language] method named \`methodName\` that [description].\n\nExample:\n\nInput: \`[input]\`\nOutput: \`[output]\`",
  "hints": ["hint1", "hint2", "hint3"]
}

EXAMPLE OUTPUT:
{
  "challenge": "Problem: Array Deduplication\n\nWrite a Java method named \`deduplicateArray\` that takes an integer array as input and returns a new array containing only the unique elements from the original array, preserving the original order of the elements.\n\nExample:\n\nInput: \`[1, 2, 2, 3, 4, 4, 5]\`\nOutput: \`[1, 2, 3, 4, 5]\`",
  "hints": ["Use a Set to track seen elements", "Iterate through the array once", "Preserve the original order of first occurrences"]
}

REQUIREMENTS:
- Use ${difficulty} difficulty level
- Focus on ${topics} concepts
- Include 2-3 helpful hints
- Return ONLY the JSON object, no other text
- Make challenge solvable in 30-45 minutes

Generate a ${skillName} challenge for ${topics} now.`;

    const prompt = `Create a ${difficulty.toLowerCase()} level coding challenge for ${skillName} focusing on ${topics}.

Return ONLY a JSON object with this structure:
{
  "challenge": "Problem: [Title]\\n\\nWrite a ${skillName} method named \`methodName\` that [description].\\n\\nExample:\\n\\nInput: \`[input]\`\\nOutput: \`[output]\`",
  "hints": ["hint1", "hint2", "hint3"]
}

Focus on ${topics} concepts and include 2-3 helpful hints. Return ONLY the JSON, no other text.`;

    this.aiService.getAiResponse(prompt, systemInstruction).subscribe({
      next: (response: any) => {
        try {
          console.log('AI Response for challenge:', response);

          let responseText = '';

          // Extract the response text from different formats
          if (response.content) {
            if (typeof response.content === 'string') {
              responseText = response.content;
            } else if (response.content.response) {
              responseText = response.content.response;
            } else if (response.content.content) {
              responseText = response.content.content;
            }
          } else if (response.response) {
            responseText = response.response;
          } else if (typeof response === 'string') {
            responseText = response;
          }

          responseText = responseText.trim();

          // Debug the response
          console.log('Raw responseText:', responseText);
          console.log('Type of responseText:', typeof responseText);

          let challengeText = '';
          let hints: string[] = [];

          // Check if the response is already the raw JSON string we need to parse
          if (responseText.includes('"challenge":') && responseText.includes('"hints":')) {
            try {
              // Parse the JSON string
              const parsedData = JSON.parse(responseText);
              console.log('Parsed JSON data:', parsedData);

              challengeText = parsedData.challenge || '';
              hints = parsedData.hints || [];

              console.log('Extracted challenge:', challengeText);
              console.log('Extracted hints:', hints);
            } catch (e) {
              console.error('Failed to parse JSON:', e);
              // If JSON parsing fails, try to extract manually
              const challengeMatch = responseText.match(/"challenge":\s*"([^"]+)"/);
              if (challengeMatch) {
                challengeText = challengeMatch[1].replace(/\\n/g, '\n');
              } else {
                challengeText = responseText;
              }
            }
          } else {
            // If it's not JSON format, treat as plain text
            challengeText = responseText;
          }

          // Clean up the challenge text
          challengeText = challengeText.trim();

          // Remove any extra requirements text that might be appended
          const requirementsIndex = challengeText.indexOf('REQUIREMENTS:');
          if (requirementsIndex !== -1) {
            challengeText = challengeText.substring(0, requirementsIndex).trim();
          }

          if (challengeText && challengeText.length > 10) {
            this.challengeContent = challengeText;
            this.challengeHints = hints;
            this.isGenerating = false;
            this.toastService.success('Challenge generated successfully!');
          } else {
            throw new Error('Empty or invalid challenge content');
          }
        } catch (error) {
          console.error('Error parsing AI response:', error);
          this.isGenerating = false;
          this.toastService.error('Failed to parse AI response. Please try again.');
        }
      },
      error: (error: any) => {
        console.error('Error generating challenge:', error);
        this.isGenerating = false;
        this.toastService.error('Failed to generate challenge. Please try again.');
      }
    });
  }





  /**
   * Check if schedule has ever had questions
   */
  hasEverHadQuestions(): boolean {
    const schedule = this.schedule as any;
    return !!(schedule?.lastUpdatedAt || schedule?.lastUpdatedBy || schedule?.questionsCount > 0);
  }

  /**
   * Save draft
   */
  saveDraft(): void {
    if (!this.challengeContent.trim()) {
      this.toastService.error('No challenge content to save');
      return;
    }

    const scheduleId = this.schedule?.id || this.route.snapshot.params['scheduleId'];
    if (!scheduleId) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isSaving = true;

    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      challengeContent: this.challengeContent,
      challengeHints: this.challengeHints,
      published: false,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || ''
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isSaving = false;
        this.toastService.success('Challenge saved as draft successfully');
      },
      error: (error: any) => {
        console.error('Error saving draft:', error);
        this.isSaving = false;
        this.toastService.error('Failed to save draft. Please try again.');
      }
    });
  }

  /**
   * Publish challenge
   */
  publishQuestions(): void {
    if (!this.challengeContent.trim()) {
      this.toastService.error('No challenge content to publish');
      return;
    }

    const scheduleId = this.schedule?.id || this.route.snapshot.params['scheduleId'];
    if (!scheduleId) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isPublishing = true;

    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      challengeContent: this.challengeContent,
      challengeHints: this.challengeHints,
      published: true,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || ''
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isPublishing = false;
        this.toastService.success('Challenge published successfully');
      },
      error: (error: any) => {
        console.error('Error publishing challenge:', error);
        this.isPublishing = false;
        this.toastService.error('Failed to publish challenge. Please try again.');
      }
    });
  }

}
