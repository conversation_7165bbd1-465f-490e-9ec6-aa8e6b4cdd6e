import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { ToastService } from '../../../../core/services/toast.service';
import { AiService } from '../../../../core/services/ai.service';
import { FirestoreService } from '../../../../core/services/firestore.service';
import { Auth } from '@angular/fire/auth';
import { ActivatedRoute } from '@angular/router';

// Simplified interface for challenge content
export interface ChallengeData {
  content: string;
  skillName: string;
  testTopic: string;
  difficulty: string;
  lastUpdatedAt?: Date;
  lastUpdatedBy?: string;
  published?: boolean;
}

@Component({
  selector: 'app-challenge-generation',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './challenge-generation.component.html',
  styleUrls: ['./challenge-generation.component.less']
})
export class ChallengeGenerationComponent implements OnInit, OnDestroy {
  @Input() schedule: any;

  // Component state
  generatedChallenges: any[] = [];
  selectedChallengeIndex: number = 0;
  manualChallengeContent: string = '';
  isGenerating: boolean = false;
  isSaving: boolean = false;
  isPublishing: boolean = false;

  private destroy$ = new Subject<void>();



  constructor(
    private toastService: ToastService,
    private aiService: AiService,
    private firestoreService: FirestoreService,
    private auth: Auth,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.loadExistingContent();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load existing challenge content from schedule
   */
  loadExistingContent(): void {
    if (this.schedule?.generatedChallenges && this.schedule.generatedChallenges.length > 0) {
      this.generatedChallenges = this.schedule.generatedChallenges;
      // Load the previously selected index, default to 0 if not set
      this.selectedChallengeIndex = this.schedule.selectedChallengeIndex ?? 0;
    }
    if (this.schedule?.manualChallengeContent) {
      this.manualChallengeContent = this.schedule.manualChallengeContent;
    }
  }

  /**
   * Generate challenges with AI
   */
  generateChallengeWithAI(): void {
    if (!this.schedule) {
      this.toastService.error('Schedule data not available');
      return;
    }

    this.isGenerating = true;

    const skillName = this.schedule.skillName || 'Programming';
    const topics = this.schedule.challengeTopic || this.schedule.testTopic || 'General Programming';
    const difficulty = this.schedule.difficulty || 'Medium';

    const systemInstruction = `You are an expert coding interviewer and curriculum planner for a technical training platform. Your task is to generate high-quality, relevant coding challenges that match a given skill area, topic, and difficulty level. These challenges are scheduled by a trainer immediately after a class, so they must:

- Reinforce recently taught concepts
- Be suitable for solving in an online compiler (no GUI or I/O complexity)
- Mimic real interview-style problems
- Include exactly three helpful hints per challenge
- Avoid boilerplate or heavy setup

Be structured in this format:

[
  {
    "challenge": "Problem statement here...",
    "hints": [
      "Hint 1",
      "Hint 2",
      "Hint 3"
    ]
  },
  ...
]`;

    const prompt = `Generate 3 coding challenges based on the following:

Skill: ${skillName}
Topic: ${topics}
Difficulty: ${difficulty}

Format the output as a JSON array. Each challenge should be concise, clear, interview-style, and solvable in an online compiler with no external tools. Each challenge should include 3 thoughtful hints.`;

    this.aiService.getGPTResponse(prompt, systemInstruction, 'challenge').subscribe({
      next: (response: any) => {
        try {
          console.log('GPT Response:', response);

          // Parse the response according to the new format
          let challengesData = JSON.parse(response.reply.choices[0].message.content)?.challenges;

          if (Array.isArray(challengesData) && challengesData.length > 0) {
            this.generatedChallenges = challengesData;
            this.selectedChallengeIndex = 0; // Select first challenge by default
            this.isGenerating = false;
            this.toastService.success(`Generated ${challengesData.length} challenges successfully!`);
          } else {
            throw new Error('Invalid response format - expected array of challenges');
          }
        } catch (error) {
          console.error('Error parsing GPT response:', error);
          this.isGenerating = false;
          this.toastService.error('Failed to parse AI response. Please try again.');
        }
      },
      error: (error: any) => {
        console.error('Error generating challenges:', error);
        this.isGenerating = false;
        this.toastService.error('Failed to generate challenges. Please try again.');
      }
    });
  }





  /**
   * Check if schedule has ever had questions
   */
  hasEverHadQuestions(): boolean {
    const schedule = this.schedule as any;
    return !!(schedule?.lastUpdatedAt || schedule?.lastUpdatedBy || schedule?.questionsCount > 0);
  }

  /**
   * Save draft
   */
  saveDraft(): void {
    const finalContent = this.getFinalChallengeContent();
    if (!finalContent.trim()) {
      this.toastService.error('No challenge content to save');
      return;
    }

    const scheduleId = this.schedule?.id || this.route.snapshot.params['scheduleId'];
    if (!scheduleId) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isSaving = true;

    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      generatedChallenges: this.generatedChallenges,
      selectedChallengeIndex: this.selectedChallengeIndex,
      manualChallengeContent: this.manualChallengeContent,
      published: false,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || ''
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isSaving = false;
        this.toastService.success('Challenge saved as draft successfully');
      },
      error: (error: any) => {
        console.error('Error saving draft:', error);
        this.isSaving = false;
        this.toastService.error('Failed to save draft. Please try again.');
      }
    });
  }

  /**
   * Publish challenge
   */
  publishQuestions(): void {
    const finalContent = this.getFinalChallengeContent();
    if (!finalContent.trim()) {
      this.toastService.error('No challenge content to publish');
      return;
    }

    const scheduleId = this.schedule?.id || this.route.snapshot.params['scheduleId'];
    if (!scheduleId) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isPublishing = true;

    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      generatedChallenges: this.generatedChallenges,
      selectedChallengeIndex: this.selectedChallengeIndex,
      manualChallengeContent: this.manualChallengeContent,
      published: true,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || ''
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isPublishing = false;

        // Update local state to show only published content
        if (this.selectedChallengeIndex >= 0) {
          // If AI challenge was selected, keep only that challenge
          const selectedChallenge = this.generatedChallenges[this.selectedChallengeIndex];
          this.generatedChallenges = [selectedChallenge];
          this.selectedChallengeIndex = 0; // Reset to first (and only) challenge
          this.manualChallengeContent = ''; // Clear manual content
        } else {
          // If manual content was used, clear AI challenges
          this.generatedChallenges = [];
          this.selectedChallengeIndex = -1;
          // Keep manual content as is
        }

        this.toastService.success('Challenge published successfully');
      },
      error: (error: any) => {
        console.error('Error publishing challenge:', error);
        this.isPublishing = false;
        this.toastService.error('Failed to publish challenge. Please try again.');
      }
    });
  }

  /**
   * Select a challenge by index
   */
  selectChallenge(index: number): void {
    this.selectedChallengeIndex = index;
    // Clear manual content when AI challenge is selected
    if (index >= 0) {
      this.manualChallengeContent = '';
    }
  }

  /**
   * Get the currently selected challenge
   */
  getSelectedChallenge(): any {
    if (this.selectedChallengeIndex >= 0 && this.selectedChallengeIndex < this.generatedChallenges.length) {
      return this.generatedChallenges[this.selectedChallengeIndex];
    }
    return null;
  }

  /**
   * Get the final challenge content (either selected AI challenge or manual content)
   */
  getFinalChallengeContent(): string {
    const selectedChallenge = this.getSelectedChallenge();
    return selectedChallenge ? selectedChallenge.challenge : this.manualChallengeContent;
  }

  /**
   * Check if there are unsaved changes
   */
  hasUnsavedChanges(): boolean {
    // Check if there are generated challenges or manual content that haven't been saved
    return this.generatedChallenges.length > 0 || this.manualChallengeContent.trim().length > 0;
  }

  /**
   * Generate 3 more challenges (replace existing ones)
   */
  generateMoreChallenges(): void {
    this.generateChallengeWithAI();
  }

  /**
   * Handle manual content change - unselect AI challenges when user types
   */
  onManualContentChange(): void {
    // If user starts typing in manual textarea, unselect any AI challenge
    if (this.manualChallengeContent.trim().length > 0) {
      this.selectedChallengeIndex = -1; // -1 means no AI challenge selected
    }
  }

}
