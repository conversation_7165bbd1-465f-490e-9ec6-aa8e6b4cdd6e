import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { ToastService } from '../../../../core/services/toast.service';
import { AiService } from '../../../../core/services/ai.service';
import { FirestoreService } from '../../../../core/services/firestore.service';
import { Auth } from '@angular/fire/auth';
import { ActivatedRoute } from '@angular/router';

// Simplified interface for challenge content
export interface ChallengeData {
  content: string;
  skillName: string;
  testTopic: string;
  difficulty: string;
  lastUpdatedAt?: Date;
  lastUpdatedBy?: string;
  published?: boolean;
}

@Component({
  selector: 'app-challenge-generation',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './challenge-generation.component.html',
  styleUrls: ['./challenge-generation.component.less']
})
export class ChallengeGenerationComponent implements OnInit, OnDestroy {
  @Input() schedule: any;

  // Component state
  challengeContent: string = '';
  isGenerating: boolean = false;
  isSaving: boolean = false;
  isPublishing: boolean = false;

  private destroy$ = new Subject<void>();



  constructor(
    private toastService: ToastService,
    private aiService: AiService,
    private firestoreService: FirestoreService,
    private auth: Auth,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.loadExistingContent();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load existing challenge content from schedule
   */
  loadExistingContent(): void {
    if (this.schedule?.challengeContent) {
      this.challengeContent = this.schedule.challengeContent;
    }
  }

  /**
   * Generate challenge with AI
   */
  generateChallengeWithAI(): void {
    if (!this.schedule) {
      this.toastService.error('Schedule data not available');
      return;
    }

    this.isGenerating = true;

    const skillName = this.schedule.skillName || 'Programming';
    const topics = this.schedule.challengeTopic || this.schedule.testTopic || 'General Programming';
    const difficulty = this.schedule.difficulty || 'Medium';

    const systemInstruction = `You are an expert coding challenge creator for EdTech companies. Create post-class coding challenges for students to practice immediately after their lesson.

CONTEXT: Students just completed a class on ${skillName} - ${topics}. Create a challenge that reinforces these concepts.

STRICT OUTPUT FORMAT - Follow this EXACT structure:

Problem: [Problem Title]

Write a [language] method named \`methodName\` that [clear description of what the method should do].

Example:

Input: \`[sample input]\`
Output: \`[expected output]\`

REQUIREMENTS:
1. Start with "Problem:" followed by a concise title
2. Write method description with specific method name in backticks
3. Include ONE clear example with Input/Output in backticks
4. Use ${difficulty} difficulty level appropriate for ${skillName}
5. Focus on ${topics} concepts
6. Keep it concise and practical
7. Make it solvable in 30-45 minutes
8. Return ONLY the challenge text, no extra formatting or explanations

EXAMPLE:
Problem: Array Deduplication

Write a Java method named \`deduplicateArray\` that takes an integer array as input and returns a new array containing only the unique elements from the original array, preserving the original order of the elements.

Example:

Input: \`[1, 2, 2, 3, 4, 4, 5]\`
Output: \`[1, 2, 3, 4, 5]\`

Generate a similar challenge for ${skillName} - ${topics}.`;

    const prompt = `Create a ${difficulty.toLowerCase()} level coding challenge for ${skillName} focusing on ${topics}.

Follow the EXACT format shown in the system instruction:
- Start with "Problem:" and a title
- Write method description with method name in backticks
- Include ONE example with Input/Output in backticks
- Keep it concise and focused on ${topics}
- Make it appropriate for ${difficulty} level

Generate the challenge now.`;

    this.aiService.getAiResponse(prompt, systemInstruction).subscribe({
      next: (response: any) => {
        try {
          console.log('AI Response for challenge:', response);

          let challengeText = '';

          // Handle different response formats
          if (response.content) {
            if (typeof response.content === 'string') {
              challengeText = response.content;
            } else if (response.content.response) {
              challengeText = response.content.response;
            } else if (response.content.content) {
              challengeText = response.content.content;
            }
          } else if (response.response) {
            challengeText = response.response;
          } else if (typeof response === 'string') {
            challengeText = response;
          }

          // Clean up the response
          challengeText = challengeText.trim();

          // Remove any JSON formatting if present
          if (challengeText.startsWith('{') && challengeText.endsWith('}')) {
            try {
              const parsed = JSON.parse(challengeText);
              challengeText = parsed.content || parsed.response || parsed.challenge || challengeText;
            } catch (e) {
              // If JSON parsing fails, use the original text
            }
          }

          // Remove markdown formatting
          challengeText = challengeText.replace(/```[\s\S]*?```/g, '');
          challengeText = challengeText.replace(/`([^`]+)`/g, '`$1`'); // Keep method names in backticks
          challengeText = challengeText.trim();

          if (challengeText && challengeText.length > 10) {
            this.challengeContent = challengeText;
            this.isGenerating = false;
            this.toastService.success('Challenge generated successfully!');
          } else {
            throw new Error('Empty or invalid response from AI');
          }
        } catch (error) {
          console.error('Error parsing AI response:', error);
          this.isGenerating = false;
          this.toastService.error('Failed to parse AI response. Please try again.');
        }
      },
      error: (error: any) => {
        console.error('Error generating challenge:', error);
        this.isGenerating = false;
        this.toastService.error('Failed to generate challenge. Please try again.');
      }
    });
  }





  /**
   * Check if schedule has ever had questions
   */
  hasEverHadQuestions(): boolean {
    const schedule = this.schedule as any;
    return !!(schedule?.lastUpdatedAt || schedule?.lastUpdatedBy || schedule?.questionsCount > 0);
  }

  /**
   * Save draft
   */
  saveDraft(): void {
    if (!this.challengeContent.trim()) {
      this.toastService.error('No challenge content to save');
      return;
    }

    const scheduleId = this.schedule?.id || this.route.snapshot.params['scheduleId'];
    if (!scheduleId) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isSaving = true;

    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      challengeContent: this.challengeContent,
      published: false,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || ''
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isSaving = false;
        this.toastService.success('Challenge saved as draft successfully');
      },
      error: (error: any) => {
        console.error('Error saving draft:', error);
        this.isSaving = false;
        this.toastService.error('Failed to save draft. Please try again.');
      }
    });
  }

  /**
   * Publish challenge
   */
  publishQuestions(): void {
    if (!this.challengeContent.trim()) {
      this.toastService.error('No challenge content to publish');
      return;
    }

    const scheduleId = this.schedule?.id || this.route.snapshot.params['scheduleId'];
    if (!scheduleId) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isPublishing = true;

    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      challengeContent: this.challengeContent,
      published: true,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || ''
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isPublishing = false;
        this.toastService.success('Challenge published successfully');
      },
      error: (error: any) => {
        console.error('Error publishing challenge:', error);
        this.isPublishing = false;
        this.toastService.error('Failed to publish challenge. Please try again.');
      }
    });
  }

}
