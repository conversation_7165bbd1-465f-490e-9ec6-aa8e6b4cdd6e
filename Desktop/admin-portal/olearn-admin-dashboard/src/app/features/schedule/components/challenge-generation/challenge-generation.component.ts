import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { ToastService } from '../../../../core/services/toast.service';
import { AiService } from '../../../../core/services/ai.service';
import { FirestoreService } from '../../../../core/services/firestore.service';
import { Auth } from '@angular/fire/auth';
import { ActivatedRoute } from '@angular/router';

// Simplified interface for challenge content
export interface ChallengeData {
  content: string;
  skillName: string;
  testTopic: string;
  difficulty: string;
  lastUpdatedAt?: Date;
  lastUpdatedBy?: string;
  published?: boolean;
}

@Component({
  selector: 'app-challenge-generation',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './challenge-generation.component.html',
  styleUrls: ['./challenge-generation.component.less']
})
export class ChallengeGenerationComponent implements OnInit, OnDestroy {
  @Input() schedule: any;

  // Component state
  challengeContent: string = '';
  isGenerating: boolean = false;
  isSaving: boolean = false;
  isPublishing: boolean = false;

  private destroy$ = new Subject<void>();



  constructor(
    private toastService: ToastService,
    private aiService: AiService,
    private firestoreService: FirestoreService,
    private auth: Auth,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.loadExistingContent();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load existing challenge content from schedule
   */
  loadExistingContent(): void {
    if (this.schedule?.challengeContent) {
      this.challengeContent = this.schedule.challengeContent;
    }
  }

  /**
   * Generate challenge with AI
   */
  generateChallengeWithAI(): void {
    if (!this.schedule) {
      this.toastService.error('Schedule data not available');
      return;
    }

    this.isGenerating = true;

    const skillName = this.schedule.skillName || 'Programming';
    const topics = this.schedule.challengeTopic || this.schedule.testTopic || 'General Programming';
    const difficulty = this.schedule.difficulty || 'Medium';

    const systemInstruction = `You are an expert coding challenge creator for EdTech companies. You create post-class coding challenges that help students practice what they just learned.

CONTEXT: This challenge will be assigned to students immediately after a class session on ${skillName} - ${topics}.

REQUIREMENTS:
1. Create a clear, practical coding problem that reinforces the class concepts
2. Write a detailed problem statement with specific requirements
3. Include concrete input/output examples with explanations
4. Specify constraints, edge cases, and expected time/space complexity
5. Make it appropriate for ${difficulty} difficulty level
6. Focus specifically on ${skillName} and ${topics} concepts
7. Ensure the problem is solvable within 30-60 minutes
8. Return ONLY the challenge text in plain format, no JSON or special formatting

EXAMPLE FORMAT:
Problem: Write a Java method that takes a sentence (a string) as input and returns the sentence with each word reversed, but the word order remains the same.

Example:
Input: "Hello World"
Output: "olleH dlroW"

Explanation: Each word is reversed individually, but their positions remain unchanged.

Constraints:
- Input string length: 1 ≤ n ≤ 1000
- Words are separated by single spaces
- No leading or trailing spaces

The challenge should test practical application of ${topics} concepts learned in class.`;

    const prompt = `Create a ${difficulty.toLowerCase()} level coding challenge for students who just completed a class on ${skillName} - ${topics}.

Requirements:
- Problem should reinforce the ${topics} concepts just taught
- Include clear problem statement with specific requirements
- Provide concrete input/output examples with explanations
- Specify constraints and edge cases
- Make it practical and achievable for students
- Ensure it tests understanding of ${topics} in a real-world context

Generate a comprehensive post-class coding challenge.`;

    this.aiService.getAiResponse(prompt, systemInstruction).subscribe({
      next: (response: any) => {
        try {
          console.log('AI Response for challenge:', response);

          let challengeText = '';
          if (response.content && typeof response.content === 'object' && response.content.response) {
            challengeText = response.content.response;
          } else if (typeof response.content === 'string') {
            challengeText = response.content;
          } else {
            challengeText = response.response || '';
          }

          if (challengeText.trim()) {
            this.challengeContent = challengeText.trim();
            this.isGenerating = false;
            this.toastService.success('Challenge generated successfully!');
          } else {
            throw new Error('Empty response from AI');
          }
        } catch (error) {
          console.error('Error parsing AI response:', error);
          this.isGenerating = false;
          this.toastService.error('Failed to parse AI response. Please try again.');
        }
      },
      error: (error: any) => {
        console.error('Error generating challenge:', error);
        this.isGenerating = false;
        this.toastService.error('Failed to generate challenge. Please try again.');
      }
    });
  }





  /**
   * Check if schedule has ever had questions
   */
  hasEverHadQuestions(): boolean {
    const schedule = this.schedule as any;
    return !!(schedule?.lastUpdatedAt || schedule?.lastUpdatedBy || schedule?.questionsCount > 0);
  }

  /**
   * Save draft
   */
  saveDraft(): void {
    if (!this.challengeContent.trim()) {
      this.toastService.error('No challenge content to save');
      return;
    }

    const scheduleId = this.schedule?.id || this.route.snapshot.params['scheduleId'];
    if (!scheduleId) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isSaving = true;

    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      challengeContent: this.challengeContent,
      published: false,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || ''
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isSaving = false;
        this.toastService.success('Challenge saved as draft successfully');
      },
      error: (error: any) => {
        console.error('Error saving draft:', error);
        this.isSaving = false;
        this.toastService.error('Failed to save draft. Please try again.');
      }
    });
  }

  /**
   * Publish challenge
   */
  publishQuestions(): void {
    if (!this.challengeContent.trim()) {
      this.toastService.error('No challenge content to publish');
      return;
    }

    const scheduleId = this.schedule?.id || this.route.snapshot.params['scheduleId'];
    if (!scheduleId) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isPublishing = true;

    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      challengeContent: this.challengeContent,
      published: true,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || ''
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isPublishing = false;
        this.toastService.success('Challenge published successfully');
      },
      error: (error: any) => {
        console.error('Error publishing challenge:', error);
        this.isPublishing = false;
        this.toastService.error('Failed to publish challenge. Please try again.');
      }
    });
  }

}
