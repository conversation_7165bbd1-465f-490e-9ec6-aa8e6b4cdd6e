import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { ToastService } from '../../../../core/services/toast.service';
import { AiService } from '../../../../core/services/ai.service';
import { FirestoreService } from '../../../../core/services/firestore.service';
import { Auth } from '@angular/fire/auth';
import { ActivatedRoute } from '@angular/router';

export interface ChallengeQuestion {
  id: string;
  question: string;
  code: string;
  language: string;
  options: { text: string; isCorrect: boolean }[];
  explanation: string;
  difficulty: string;
  type: string;
  topicsCovered: string[];
}

@Component({
  selector: 'app-challenge-generation',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './challenge-generation.component.html',
  styleUrls: ['./challenge-generation.component.less']
})
export class ChallengeGenerationComponent implements OnInit, OnDestroy {
  @Input() schedule: any;

  // Component state
  generatedQuestions: ChallengeQuestion[] = [];
  isGenerating: boolean = false;
  isSaving: boolean = false;
  isPublishing: boolean = false;

  // AI Integration
  aiPrompt: string = '';
  isGeneratingFromPrompt: boolean = false;
  smartSuggestions: string[] = [];

  // PDF Upload
  selectedFile: File | null = null;
  isDragOver: boolean = false;
  isGeneratingFromPDF: boolean = false;

  private destroy$ = new Subject<void>();

  /**
   * Get option label (A, B, C, D)
   */
  getOptionLabel(index: number): string {
    return String.fromCharCode(65 + index);
  }

  constructor(
    private toastService: ToastService,
    private aiService: AiService,
    private firestoreService: FirestoreService,
    private auth: Auth,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.loadExistingQuestions();
    this.generateSmartSuggestions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load existing questions from schedule
   */
  loadExistingQuestions(): void {
    if (this.schedule?.generatedQuestions) {
      this.generatedQuestions = this.schedule.generatedQuestions.map((q: any, index: number) => ({
        id: q.id || `existing_${index}`,
        question: q.question,
        code: q.code || '',
        language: q.language || '',
        options: q.options || [],
        explanation: q.explanation || '',
        difficulty: q.difficulty || this.schedule.difficulty || 'Medium',
        type: q.type || 'Coding Challenge',
        topicsCovered: q.topicsCovered || [this.schedule.testTopic || 'Programming']
      }));
    }
  }

  /**
   * Generate smart suggestions for AI prompts
   */
  generateSmartSuggestions(): void {
    this.smartSuggestions = [];

    const requiredQuestions = this.schedule?.numberOfQuestions || 10;
    const currentQuestions = this.generatedQuestions.length;

    if (currentQuestions < requiredQuestions) {
      const remaining = requiredQuestions - currentQuestions;
      const topics = this.schedule?.testTopic || 'Programming';
      const difficulty = this.schedule?.difficulty || 'Medium';

      this.smartSuggestions = [
        `Generate ${remaining} coding challenges on ${topics}`,
        `Create ${Math.min(3, remaining)} ${difficulty.toLowerCase()} level algorithm problems`,
        `Generate data structure challenges for ${topics}`,
        `Create problem-solving questions with code examples`
      ];
    }
  }

  /**
   * Get system instruction for AI
   */
  getSystemInstruction(): string {
    return `You must return ONLY a valid JSON array of coding challenge questions. No other text, no explanations, no markdown formatting.

STRICT OUTPUT FORMAT - Return exactly this structure:
[
    {
        "question": "problem statement with clear requirements",
        "code": "starter code or example if applicable",
        "language": "programming language (e.g., javascript, python, java)",
        "options": ["option1", "option2", "option3", "option4"],
        "explanation": "solution explanation with approach",
        "answerIndex": 0,
        "topicsCovered": ["topic"]
    }
]

CRITICAL REQUIREMENTS:
- Return ONLY the JSON array, nothing else
- Focus on coding challenges and algorithmic problems
- Include code snippets when relevant
- Provide clear problem statements
- Ensure valid JSON syntax
- answerIndex must be 0-based integer (0, 1, 2, or 3)

Generate technical coding challenge questions that test programming skills and problem-solving abilities.`;
  }

  /**
   * Generate initial questions
   */
  generateQuestions(): void {
    if (!this.schedule) {
      this.toastService.error('Schedule data not available');
      return;
    }

    this.isGenerating = true;

    const skillName = this.schedule.skillName || 'Programming';
    const topics = this.schedule.testTopic || 'General Programming';
    const difficulty = this.schedule.difficulty || 'Medium';
    const questionCount = this.schedule.numberOfQuestions || 10;

    const prompt = `Generate ${questionCount} ${difficulty.toLowerCase()} level coding challenge questions for ${skillName} focusing on ${topics}.

Context:
- Skill: ${skillName}
- Topics: ${topics}
- Difficulty: ${difficulty}
- Question Type: Coding Challenge
- Number of Questions: ${questionCount}

Create challenging programming problems that test algorithmic thinking and coding skills.`;

    const systemInstruction = this.getSystemInstruction();

    this.aiService.getAiResponse(prompt, systemInstruction).subscribe({
      next: (response: any) => {
        try {
          console.log('AI Response for challenges:', response);

          let questionsData;
          if (typeof response.content === 'string') {
            questionsData = JSON.parse(response.content);
          } else {
            questionsData = response.content;
          }

          if (Array.isArray(questionsData)) {
            this.generatedQuestions = questionsData.map((q: any, index: number) => ({
              id: `challenge_${Date.now()}_${index}`,
              question: q.question,
              code: q.code || '',
              language: q.language || 'javascript',
              options: q.options?.map((opt: string, optIndex: number) => ({
                text: opt,
                isCorrect: optIndex === q.answerIndex
              })) || [],
              explanation: q.explanation || '',
              difficulty: difficulty,
              type: 'Coding Challenge',
              topicsCovered: q.topicsCovered || [topics]
            }));

            this.isGenerating = false;
            this.generateSmartSuggestions();
            this.toastService.success(`Generated ${this.generatedQuestions.length} coding challenges successfully!`);
          } else {
            throw new Error('Invalid response format');
          }
        } catch (error) {
          console.error('Error parsing AI response:', error);
          this.isGenerating = false;
          this.toastService.error('Failed to parse AI response. Please try again.');
        }
      },
      error: (error: any) => {
        console.error('Error generating challenges:', error);
        this.isGenerating = false;
        this.toastService.error('Failed to generate challenges. Please try again.');
      }
    });
  }

  /**
   * Submit AI prompt
   */
  submitAIPrompt(): void {
    if (!this.aiPrompt.trim() || this.isGeneratingFromPrompt) {
      return;
    }

    if (!this.schedule) {
      this.toastService.error('Schedule data not available');
      return;
    }

    this.isGeneratingFromPrompt = true;

    const skillName = this.schedule.skillName || 'Programming';
    const topics = this.schedule.testTopic || 'General Programming';
    const difficulty = this.schedule.difficulty || 'Medium';

    // Calculate remaining questions
    const totalRequired = this.schedule.numberOfQuestions || 10;
    const currentCount = this.generatedQuestions.length;
    const remainingCount = Math.max(0, totalRequired - currentCount);

    if (remainingCount <= 0) {
      this.toastService.info(`All ${totalRequired} challenges have been generated. Schedule is complete.`);
      this.isGeneratingFromPrompt = false;
      return;
    }

    let contextInfo = '';
    if (this.generatedQuestions.length === 0) {
      contextInfo = `
Context:
- Skill: ${skillName}
- Topics: ${topics}
- Difficulty: ${difficulty}
- Question Type: Coding Challenge
- Schedule Type: challenge

`;
    }

    const prompt = `${contextInfo}Generate ${remainingCount} ${difficulty.toLowerCase()} level coding challenge questions for ${skillName} focusing on ${topics}.

User Request: "${this.aiPrompt.trim()}"

Generate challenging programming problems that test algorithmic thinking and coding skills.`;

    const systemInstruction = this.getSystemInstruction();

    this.aiService.getAiResponse(prompt, systemInstruction).subscribe({
      next: (response: any) => {
        try {
          let questionsData;
          if (typeof response.content === 'string') {
            questionsData = JSON.parse(response.content);
          } else {
            questionsData = response.content;
          }

          if (Array.isArray(questionsData)) {
            const newQuestions = questionsData.map((q: any, index: number) => ({
              id: `prompt_${Date.now()}_${index}`,
              question: q.question,
              code: q.code || '',
              language: q.language || 'javascript',
              options: q.options?.map((opt: string, optIndex: number) => ({
                text: opt,
                isCorrect: optIndex === q.answerIndex
              })) || [],
              explanation: q.explanation || '',
              difficulty: difficulty,
              type: 'Coding Challenge',
              topicsCovered: q.topicsCovered || [topics]
            }));

            this.generatedQuestions.push(...newQuestions);
            this.aiPrompt = '';
            this.smartSuggestions = [];
            this.generateSmartSuggestions();

            this.isGeneratingFromPrompt = false;
            this.toastService.success(`Generated ${newQuestions.length} challenges successfully!`);
          } else {
            throw new Error('Invalid response format');
          }
        } catch (error) {
          console.error('Error parsing AI response from prompt:', error);
          this.isGeneratingFromPrompt = false;
          this.toastService.error('Failed to parse AI response. Please try again.');
        }
      },
      error: (error: any) => {
        console.error('Error generating challenges from prompt:', error);
        this.isGeneratingFromPrompt = false;
        this.toastService.error('Failed to generate challenges. Please try again.');
      }
    });
  }

  /**
   * Delete a question
   */
  deleteQuestion(questionId: string): void {
    this.generatedQuestions = this.generatedQuestions.filter(q => q.id !== questionId);
    
    if (this.generatedQuestions.length === 0) {
      this.autoSaveEmptyState();
    } else {
      this.generateSmartSuggestions();
    }
    
    this.toastService.success('Challenge deleted successfully');
  }

  /**
   * Auto-save empty state
   */
  private autoSaveEmptyState(): void {
    const scheduleId = this.schedule?.id || this.route.snapshot.params['scheduleId'];

    if (!scheduleId) {
      return;
    }

    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      generatedQuestions: [],
      published: false,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || '',
      questionsCount: 0
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        console.log('Auto-saved empty state for challenges');
      },
      error: (error: any) => {
        console.error('Error auto-saving empty state:', error);
      }
    });
  }

  /**
   * Check if schedule has ever had questions
   */
  hasEverHadQuestions(): boolean {
    const schedule = this.schedule as any;
    return !!(schedule?.lastUpdatedAt || schedule?.lastUpdatedBy || schedule?.questionsCount > 0);
  }

  /**
   * Save draft
   */
  saveDraft(): void {
    if (this.generatedQuestions.length === 0) {
      this.toastService.error('No challenges to save');
      return;
    }

    const scheduleId = this.schedule?.id || this.route.snapshot.params['scheduleId'];
    if (!scheduleId) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isSaving = true;

    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      generatedQuestions: this.generatedQuestions,
      published: false,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || '',
      questionsCount: this.generatedQuestions.length
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isSaving = false;
        this.toastService.success(`Successfully saved ${this.generatedQuestions.length} challenges as draft`);
      },
      error: (error: any) => {
        console.error('Error saving draft:', error);
        this.isSaving = false;
        this.toastService.error('Failed to save draft. Please try again.');
      }
    });
  }

  /**
   * Publish questions
   */
  publishQuestions(): void {
    if (this.generatedQuestions.length === 0) {
      this.toastService.error('No challenges to publish');
      return;
    }

    const scheduleId = this.schedule?.id || this.route.snapshot.params['scheduleId'];
    if (!scheduleId) {
      this.toastService.error('Schedule information not available');
      return;
    }

    this.isPublishing = true;

    const orgId = this.route.snapshot.params['orgId'] || 'hashinclude';
    const currentUser = this.auth.currentUser;
    const currentTime = new Date();

    const scheduleDocPath = `organizations/${orgId}/schedules/${scheduleId}`;

    const updateData = {
      generatedQuestions: this.generatedQuestions,
      published: true,
      lastUpdatedAt: currentTime,
      lastUpdatedBy: currentUser?.uid || '',
      questionsCount: this.generatedQuestions.length
    };

    this.firestoreService.updateDocumentByPath(scheduleDocPath, updateData).subscribe({
      next: () => {
        this.isPublishing = false;
        this.toastService.success(`Successfully published ${this.generatedQuestions.length} challenges`);
      },
      error: (error: any) => {
        console.error('Error publishing challenges:', error);
        this.isPublishing = false;
        this.toastService.error('Failed to publish challenges. Please try again.');
      }
    });
  }

  /**
   * PDF Upload Methods
   */
  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.handleFileSelection(files[0]);
    }
  }

  triggerFileInput(): void {
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }

  onFileSelected(event: Event): void {
    const target = event.target as HTMLInputElement;
    const files = target.files;
    if (files && files.length > 0) {
      this.handleFileSelection(files[0]);
    }
  }

  private handleFileSelection(file: File): void {
    if (file.type !== 'application/pdf') {
      this.toastService.error('Please select a PDF file');
      return;
    }

    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      this.toastService.error('File size must be less than 10MB');
      return;
    }

    this.selectedFile = file;
  }

  removeFile(event: Event): void {
    event.stopPropagation();
    this.selectedFile = null;
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Generate questions from PDF
   */
  generateQuestionsFromPDF(): void {
    if (!this.selectedFile) {
      this.toastService.error('No PDF file selected');
      return;
    }

    this.isGeneratingFromPDF = true;

    const reader = new FileReader();
    reader.onload = () => {
      const base64Data = reader.result as string;
      const base64Content = base64Data.split(',')[1];
      this.sendPDFToAI(base64Content);
    };

    reader.onerror = () => {
      this.isGeneratingFromPDF = false;
      this.toastService.error('Failed to read PDF file');
    };

    reader.readAsDataURL(this.selectedFile);
  }

  private sendPDFToAI(base64Content: string): void {
    const skillName = this.schedule?.skillName || 'Programming';
    const topics = this.schedule?.testTopic || 'General Programming';
    const difficulty = this.schedule?.difficulty || 'Medium';
    const questionCount = this.schedule?.numberOfQuestions || 10;

    const prompt = `Based on this PDF, generate ${questionCount} ${difficulty.toLowerCase()} level coding challenge questions for ${skillName} focusing on ${topics}.

Create challenging programming problems that test algorithmic thinking and coding skills.`;

    const systemInstruction = this.getSystemInstruction();

    this.aiService.getAiResponseWithPDF(prompt, systemInstruction, base64Content).subscribe({
      next: (response: any) => {
        try {
          let questionsData;
          if (typeof response.content === 'string') {
            questionsData = JSON.parse(response.content);
          } else {
            questionsData = response.content;
          }

          if (Array.isArray(questionsData)) {
            this.generatedQuestions = questionsData.map((q: any, index: number) => ({
              id: `pdf_${Date.now()}_${index}`,
              question: q.question,
              code: q.code || '',
              language: q.language || 'javascript',
              options: q.options?.map((opt: string, optIndex: number) => ({
                text: opt,
                isCorrect: optIndex === q.answerIndex
              })) || [],
              explanation: q.explanation || '',
              difficulty: difficulty,
              type: 'Coding Challenge',
              topicsCovered: q.topicsCovered || [topics]
            }));

            this.selectedFile = null;
            this.isGeneratingFromPDF = false;
            this.generateSmartSuggestions();
            this.toastService.success(`Generated ${this.generatedQuestions.length} challenges from PDF!`);
          } else {
            throw new Error('Invalid response format');
          }
        } catch (error) {
          console.error('Error parsing PDF response:', error);
          this.isGeneratingFromPDF = false;
          this.toastService.error('Failed to parse AI response. Please try again.');
        }
      },
      error: (error: any) => {
        console.error('Error generating from PDF:', error);
        this.isGeneratingFromPDF = false;
        this.toastService.error('Failed to generate challenges from PDF. Please try again.');
      }
    });
  }
}
