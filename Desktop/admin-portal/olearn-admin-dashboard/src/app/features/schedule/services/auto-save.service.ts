import { Injectable } from '@angular/core';
import { Subject, Observable } from 'rxjs';

export interface AutoSaveRequest {
  scheduleType: 'assessment' | 'interview' | 'challenge';
  scheduleId: string;
  data?: any;
}

export interface AutoSaveResponse {
  success: boolean;
  message: string;
  scheduleType: string;
}

@Injectable({
  providedIn: 'root'
})
export class AutoSaveService {
  private autoSaveSubject = new Subject<AutoSaveRequest>();
  private autoSaveResponseSubject = new Subject<AutoSaveResponse>();

  constructor() {}

  /**
   * Observable for components to listen to auto-save requests
   */
  get autoSaveRequest$(): Observable<AutoSaveRequest> {
    return this.autoSaveSubject.asObservable();
  }

  /**
   * Observable for schedule details component to listen to auto-save responses
   */
  get autoSaveResponse$(): Observable<AutoSaveResponse> {
    return this.autoSaveResponseSubject.asObservable();
  }

  /**
   * Trigger auto-save for a specific schedule type
   */
  triggerAutoSave(request: AutoSaveRequest): void {
    console.log('AutoSaveService: Triggering auto-save for', request.scheduleType);
    this.autoSaveSubject.next(request);
  }

  /**
   * Send auto-save response back to schedule details component
   */
  sendAutoSaveResponse(response: AutoSaveResponse): void {
    console.log('AutoSaveService: Sending auto-save response', response);
    this.autoSaveResponseSubject.next(response);
  }
}
