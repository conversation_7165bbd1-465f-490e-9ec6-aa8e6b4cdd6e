import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, catchError, of, switchMap, map, throwError, from } from 'rxjs';
import { AdminActivityService } from './admin-activity.service';
import { AuthService } from './auth.service';
import { Auth, getIdToken } from '@angular/fire/auth';

export interface AiRequestPayload {
  prompt: string;
  systemInstruction: string;
  model: string;
  isCompanyQuery: boolean;
  pdfContent?: string;
}

export interface AiResponse {
  response: string;
  error?: string;
}

/**
 * Service for interacting with AI models
 */
@Injectable({
  providedIn: 'root',
})
export class AiService {
  private readonly apiUrl = 'https://getgeminiresponse-n7zzdp37uq-uc.a.run.app';
  private readonly defaultModel = 'gemini-2.0-flash';

  constructor(
    private http: HttpClient,
    private adminActivityService: AdminActivityService,
    private authService: AuthService
  ) {}

  /**
   * Get AI-suggested quick actions based on admin activity
   * @param activities Optional array of admin activities. If not provided, will fetch from service.
   * @param adminName Optional admin name to use in the prompt
   * @returns Observable with array of suggested quick actions
   */
  getSuggestedQuickActions(
    activities?: any[],
    adminName: string = 'Admin'
  ): Observable<string[]> {
    // If activities are provided, use them directly
    if (activities) {
      // Format activities for the prompt
      const activityData = activities.map((activity) => {
        return {
          type: activity.category,
          action: activity.action,
          timestamp: new Date(activity.timestamp).toISOString(),
          details: activity.contextData,
        };
      });

      // Create system instruction for the AI
      const systemInstruction = `You are an AI assistant for Olearn, an educational platform.
      Your task is to analyze the admin's recent activities and suggest 6 personalized quick actions
      that would be most relevant and helpful for them right now.

      IMPORTANT: DO NOT suggest actions related to deleted items. If you see any activities with action="deleted",
      do not suggest actions related to those specific items as they no longer exist in the system.

      CRITICAL REQUIREMENTS:
      1. ALL suggestions MUST be batch-specific - every suggestion must include a batch ID
      2. NO generic suggestions allowed - every action must relate to a specific batch
      3. Use actual batch names from the activity data, not generic terms
      4. Focus on actionable items that help the admin manage their specific batches
      5. Prioritize suggestions based on recent activity patterns and specific batches mentioned
      6. Avoid suggesting actions for items that have been deleted

      Focus ONLY on suggesting "get" or "view" actions related to the current capabilities of the platform:
      - Viewing specific batch details
      - Checking specific batch student information
      - Viewing specific batch analysis or statistics
      - Checking top skills in a specific batch
      - Viewing schedules for a specific batch
      - Viewing trainer information for a specific batch

      DO NOT suggest actions that involve creating, scheduling, or modifying data as these are not yet implemented.
      NEVER suggest generic actions like "Check current schedules" or "Check Code begun students" without a specific batch.

      Each quick action should be returned as an object with these three properties:
      - suggestion: A short, actionable phrase (3-5 words) starting with a verb. ALWAYS use batch names instead of batch IDs (e.g., "View Frontend React Batch" instead of "View batch 5")
      - batch: The batch ID (e.g., "batch5") - THIS FIELD IS REQUIRED and cannot be empty
      - studentDataRequired: Boolean indicating if student data is needed (true/false)

      Examples of CORRECT suggestions:
      {
        "suggestion": "View Frontend React Batch",
        "batch": "batch5",
        "studentDataRequired": false
      },
      {
        "suggestion": "Get Frontend React students",
        "batch": "batch5",
        "studentDataRequired": true
      },
      {
        "suggestion": "Check Python Batch schedules",
        "batch": "batch3",
        "studentDataRequired": false
      }

      Return ONLY a JSON array of these objects, nothing else.`;

      // Create prompt with admin activity data
      const prompt = `Based on these recent activities by admin ${adminName},
      suggest 6 personalized quick actions that would be most relevant and helpful:
      ${JSON.stringify(activityData, null, 2)}

      Remember to return ONLY a JSON array of objects with suggestion, batch, and studentDataRequired properties.`;

      // Make the API request
      return this.getAiResponse(prompt, systemInstruction).pipe(
        catchError((error) => {
          console.error('Error getting AI suggestions:', error);
          // Rethrow the error to be handled by the caller
          return throwError(
            () => new Error('Failed to get AI suggestions: ' + error.message)
          );
        }),
        map((response: any) => {
          try {
            console.log('Raw AI response:', response);
            // Parse the response as JSON array of objects
            const suggestions = JSON.parse(response.content.response);

            // Validate that we have an array of objects with the required properties
            if (
              Array.isArray(suggestions) &&
              suggestions.every(
                (item) =>
                  typeof item === 'object' &&
                  'suggestion' in item &&
                  'batch' in item &&
                  'studentDataRequired' in item
              )
            ) {
              return suggestions;
            } else {
              throw new Error('Invalid response format');
            }
          } catch (error) {
            console.error('Error parsing AI response:', error);
            // Rethrow the error to be handled by the caller
            throw error;
          }
        })
      );
    }

    // If no activities provided, fetch them from the service
    return this.adminActivityService.getRecentActivities(20).pipe(
      catchError((error) => {
        console.error('Error fetching admin activities:', error);
        return of([]);
      }),
      switchMap((fetchedActivities) => {
        // Get admin info
        const adminId = this.authService.currentUser?.uid || '';
        const currentAdminName =
          this.authService.currentUser?.displayName || adminName;

        // Format activities for the prompt
        const activityData = fetchedActivities.map((activity) => {
          return {
            type: activity.category,
            action: activity.action,
            timestamp: new Date(activity.timestamp).toISOString(),
            details: activity.contextData,
          };
        });

        // Create system instruction for the AI
        const systemInstruction = `You are an AI assistant for Olearn, an educational platform.
        Your task is to analyze the admin's recent activities and suggest 6 personalized quick actions
        that would be most relevant and helpful for them right now.

        IMPORTANT: DO NOT suggest actions related to deleted items. If you see any activities with action="deleted",
        do not suggest actions related to those specific items as they no longer exist in the system.

        CRITICAL REQUIREMENTS:
        1. ALL suggestions MUST be batch-specific - every suggestion must include a batch ID
        2. NO generic suggestions allowed - every action must relate to a specific batch
        3. Use actual batch names from the activity data, not generic terms
        4. Focus on actionable items that help the admin manage their specific batches
        5. Prioritize suggestions based on recent activity patterns and specific batches mentioned
        6. Avoid suggesting actions for items that have been deleted

        Focus ONLY on suggesting "get" or "view" actions related to the current capabilities of the platform:
        - Viewing specific batch details
        - Checking specific batch student information
        - Viewing specific batch analysis or statistics
        - Checking top skills in a specific batch
        - Viewing schedules for a specific batch
        - Viewing trainer information for a specific batch

        DO NOT suggest actions that involve creating, scheduling, or modifying data as these are not yet implemented.
        NEVER suggest generic actions like "Check current schedules" or "Check Code begun students" without a specific batch.

        Each quick action should be returned as an object with these three properties:
        - suggestion: A short, actionable phrase (3-5 words) starting with a verb. ALWAYS use batch names instead of batch IDs (e.g., "View Frontend React Batch" instead of "View batch 5")
        - batch: The batch ID (e.g., "batch5") - THIS FIELD IS REQUIRED and cannot be empty
        - studentDataRequired: Boolean indicating if student data is needed (true/false)

        Examples of CORRECT suggestions:
        {
          "suggestion": "View Frontend React Batch",
          "batch": "batch5",
          "studentDataRequired": false
        },
        {
          "suggestion": "Get Frontend React students",
          "batch": "batch5",
          "studentDataRequired": true
        },
        {
          "suggestion": "Check Python Batch schedules",
          "batch": "batch3",
          "studentDataRequired": false
        }

        Return ONLY a JSON array of these objects, nothing else.`;

        // Create prompt with admin activity data
        const prompt = `Based on these recent activities by admin ${currentAdminName} (ID: ${adminId}),
        suggest 6 personalized quick actions that would be most relevant and helpful:
        ${JSON.stringify(activityData, null, 2)}

        Remember to return ONLY a JSON array of objects with suggestion, batch, and studentDataRequired properties.`;

        // Make the API request
        return this.getAiResponse(prompt, systemInstruction).pipe(
          catchError((error) => {
            console.error('Error getting AI suggestions:', error);
            // Rethrow the error to be handled by the caller
            return throwError(
              () => new Error('Failed to get AI suggestions: ' + error.message)
            );
          }),
          map((response) => {
            try {
              // Parse the response as JSON array of objects
              const suggestions = JSON.parse(response.response);

              // Validate that we have an array of objects with the required properties
              if (
                Array.isArray(suggestions) &&
                suggestions.every(
                  (item) =>
                    typeof item === 'object' &&
                    'suggestion' in item &&
                    'batch' in item &&
                    'studentDataRequired' in item
                )
              ) {
                return suggestions;
              } else {
                throw new Error('Invalid response format');
              }
            } catch (error) {
              console.error('Error parsing AI response:', error);
              // Rethrow the error to be handled by the caller
              throw error;
            }
          })
        );
      })
    );
  }

  /**
   * Get response from AI model
   * @param prompt The prompt to send to the AI
   * @param systemInstruction System instructions for the AI
   * @param model The AI model to use (defaults to gemini-2.0-flash)
   * @param isCompanyQuery Whether this is a company-specific query
   * @returns Observable with the AI response
   */
  getAiResponse(
    prompt: string,
    systemInstruction: string,
    model: string = this.defaultModel,
    isCompanyQuery: boolean = false
  ): Observable<AiResponse> {
    const payload: AiRequestPayload = {
      prompt,
      systemInstruction,
      model,
      isCompanyQuery,
    };

    return this.http.post<AiResponse>(this.apiUrl, payload).pipe(
      catchError((error) => {
        console.error('Error calling AI API:', error);
        return of({
          response: '',
          error: error.message || 'Failed to get AI response',
        });
      })
    );
  }

  /**
   * Get response from AI model with PDF content
   * @param prompt The prompt to send to the AI
   * @param systemInstruction System instructions for the AI
   * @param pdfContent Base64 encoded PDF content
   * @param model The AI model to use (defaults to gemini-2.0-flash)
   * @param isCompanyQuery Whether this is a company-specific query
   * @returns Observable with the AI response
   */
  getAiResponseWithPDF(
    prompt: string,
    systemInstruction: string,
    pdfContent: string,
    model: string = this.defaultModel,
    isCompanyQuery: boolean = false
  ): Observable<AiResponse> {
    const payload: AiRequestPayload = {
      prompt,
      systemInstruction,
      model,
      isCompanyQuery,
      pdfContent,
    };

    return this.http.post<AiResponse>(this.apiUrl, payload).pipe(
      catchError((error) => {
        console.error('Error calling AI API with PDF:', error);
        return of({
          response: '',
          error: error.message || 'Failed to get AI response with PDF',
        });
      })
    );
  }

  /**
   * Get GPT response using the cloud function
   * @param prompt The prompt to send to GPT
   * @param systemInstructions System instructions for GPT
   * @param type The type of request
   * @param audio Optional audio data
   * @returns Observable with the GPT response
   */
  getGPTResponse(
    prompt: string,
    systemInstructions: string,
    type: string,
    audio?: any
  ): Observable<any> {
    // Get current Firebase user and token
    const firebaseUser = this.authService.getFirebaseUser();
    if (!firebaseUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    return from(getIdToken(firebaseUser)).pipe(
      switchMap((idToken: string) => {
        const headers = {
          'Authorization': `Bearer ${idToken}`,
          'Content-Type': 'application/json',
        };

        const cloudFunctionUrl = 'https://getgptresponse-n7zzdp37uq-uc.a.run.app';
        const body = { prompt, systemInstructions, type, audio };

        return this.http.post<any>(cloudFunctionUrl, body, { headers });
      }),
      catchError((error: any) => {
        console.error('Error calling GPT API:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get AI response for a chat query about a specific batch
   * @param query The user's query
   * @param batchDetails The details of the batch to provide context
   * @param scheduleContext Optional schedule context for schedule-related queries
   * @returns Observable with the AI response
   */
  getChatResponse(
    query: string,
    batchDetails: any,
    scheduleContext?: string
  ): Observable<string> {
    // Create system instruction for the AI
    const systemInstruction = `You are an AI assistant for Olearn, an educational platform.
    Your task is to provide concise yet detailed information about batches and schedules based on the data provided.

    IMPORTANT GUIDELINES:
    1. Keep responses brief but informative - aim for 3-5 sentences for most answers
    2. Format dates in a human-readable format (e.g., "May 12, 2025" instead of "2025-05-12")
    3. Present batch details and schedules in a compact tabular format when appropriate
    4. If asked about "best" or "top" skills/students, analyze the data to determine this rather than saying you don't have enough information
    5. Make educated inferences when appropriate, but be clear when you're making an inference
    6. NEVER provide information about deleted items. If you see any activities with action="deleted", do not include information about those specific items as they no longer exist in the system.
    7. ALWAYS use batch names instead of batch IDs in your responses (e.g., "Frontend React Batch" instead of "Batch 5")
    8. When schedule information is provided, prioritize it for schedule-related queries and provide accurate, up-to-date information

    SCHEDULE-SPECIFIC GUIDELINES:
    - When asked about schedules, use the provided schedule context data if available
    - If no schedule data is found, inform the user naturally and suggest they check if schedules have been created or assigned
    - NEVER provide hardcoded or example schedule data - only use actual data from the database
    - When schedule data is available, ALWAYS present it in markdown table format for better readability
    - Present schedules in chronological order when relevant
    - Group schedules by type (tests, challenges, interviews) when helpful
    - Highlight upcoming vs completed schedules clearly
    - Include batch information when showing schedules
    - For schedule tables, include relevant columns like: Title, Type, Skill, Topic, Difficulty, Duration, Questions, Date/Time, Status

    FORMAT YOUR RESPONSES EFFICIENTLY:
    - Use markdown tables for structured data (keep tables compact)
    - Use bullet points for lists (limit to 3-5 points)
    - Use bold only for the most important information
    - Avoid lengthy explanations - be direct and to the point

    RESPONSE STRUCTURE:
    1. Direct answer to the question (1-2 sentences)
    2. Key supporting details (1-3 sentences or a small table/list)
    3. Brief insight or conclusion (1 sentence)

    FOLLOW-UP SUGGESTIONS:
    After your main response, include 3 follow-up question suggestions that the user might want to ask next.
    Format these as: "FOLLOW_UP_QUESTIONS: [question1, question2, question3]"
    These should be logical next questions based on your answer and the batch/schedule context.

    COMMON QUERIES YOU MUST HANDLE WELL:
    - Batch details (compact table with name, dates, instructor)
    - Student information (count, performance summary)
    - Skills analysis (identify top skills based on performance)
    - Performance trends (brief summary of progress)
    - Course information (key topics, completion rates)
    - Schedule information (upcoming tests, challenges, interviews)
    - Schedule conflicts or overlaps
    - Batch-specific schedules

    Remember: Your goal is to provide helpful, accurate information in a concise format, followed by relevant follow-up question suggestions.`;

    // Create prompt with batch details and optional schedule context
    let prompt = `Here are the details about the batch:
    ${JSON.stringify(batchDetails, null, 2)}`;

    // Add schedule context if provided and not empty
    if (scheduleContext && scheduleContext.trim()) {
      prompt += `\n\nHere is the current schedule information from the database:
      ${scheduleContext}`;
    } else if (scheduleContext === '') {
      // Empty context means no schedules found - let AI know this
      prompt += `\n\nNote: No schedule data was found in the database for the current context.`;
    }

    prompt += `\n\nUser's question: "${query}"

    Provide a concise but informative answer. Be direct and to the point while still giving useful information.
    Format dates in a human-readable format and use compact tables when appropriate.
    If asked about "best" or "top" skills/students, analyze the available metrics to determine this.
    ${
      scheduleContext && scheduleContext.trim()
        ? 'When answering schedule-related questions, use the provided schedule data for accurate, up-to-date information. ALWAYS format schedule information in markdown table format with relevant columns like Title, Type, Skill, Topic, Difficulty, Duration, Questions, Date/Time, and Status.'
        : scheduleContext === ''
        ? 'When answering schedule-related questions, inform the user that no schedule data was found for the current batch/context and suggest they check if schedules have been created or assigned to this batch.'
        : ''
    }

    Keep your response brief but complete, and include 3 relevant follow-up question suggestions at the end
    formatted exactly as: "FOLLOW_UP_QUESTIONS: [question1, question2, question3]"`;

    // Make the API request
    return this.getAiResponse(prompt, systemInstruction).pipe(
      map((response: any) => {
        console.log('Raw AI response:', response);
        if (!response.content.response) {
          return "I'm sorry, I couldn't process your request at this time. Please try again later.";
        }
        return response.content.response;
      }),
      catchError((error) => {
        console.error('Error getting chat response:', error);
        return of(
          "I'm sorry, I encountered an error while processing your request. Please try again later."
        );
      })
    );
  }
}
